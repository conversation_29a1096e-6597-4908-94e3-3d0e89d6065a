# راهنمای ایجاد دیتاست MPU6050

## 📋 مراحل آماده‌سازی

### 1️⃣ فایل‌های مورد نیاز برای آپلود به ESP32:

```
📁 فایل‌های ضروری:
├── mpu6050_csv_logger.py    # کد اصلی سنسور
├── config.py                # تنظیمات سنسور
└── dataset_creator.py       # اسکریپت ایجاد دیتاست

📁 فایل‌های اختیاری:
├── boot.py                  # راه‌اندازی خودکار
└── main.py                  # شروع خودکار
```

### 2️⃣ اتصالات سخت‌افزاری:

```
ESP32    →    MPU6050
3.3V     →    VCC
GND      →    GND  
GPIO21   →    SDA
GPIO22   →    SCL
```

## 🚀 نحوه استفاده

### روش 1: اجرای مستقیم روی ESP32

```python
# در REPL یا ترمینال ESP32
from dataset_creator import main
main()
```

### روش 2: استفاده از کلاس DatasetCreator

```python
from dataset_creator import DatasetCreator

# ایجاد شیء
creator = DatasetCreator("my_dataset")

# ایجاد دیتاست با 10 نمونه
files = creator.create_dataset(num_samples=10, rest_duration=2)
```

## ⚙️ پارامترهای قابل تنظیم

| پارامتر | توضیح | مقدار پیش‌فرض |
|---------|-------|----------------|
| `num_samples` | تعداد نمونه‌های مورد نیاز | 10 |
| `rest_duration` | فاصله بین نمونه‌ها (ثانیه) | 2 |
| `base_filename` | نام پایه فایل‌های CSV | "dataset_sample" |

## 📊 مشخصات هر نمونه

- **مدت زمان**: 3 ثانیه
- **فاصله نمونه‌گیری**: 0.5 ثانیه (نیم ثانیه)
- **تعداد داده در هر نمونه**: 6 داده
- **فرمت فایل**: CSV

## 📁 نام‌گذاری فایل‌ها

```
نمونه فایل ایجاد شده:
dataset_sample_001_20241128_143052.csv
dataset_sample_002_20241128_143057.csv
dataset_sample_003_20241128_143102.csv

فرمت: {base_filename}_{شماره}_{تاریخ}_{زمان}.csv
```

## 📈 فرمت داده‌های CSV

```csv
sample_id,timestamp,accel_x,accel_y,accel_z,gyro_x,gyro_y,gyro_z,temperature
1,1732800652123,0.1234,-0.5678,0.9876,12.34,-56.78,98.76,25.4
2,1732800652623,0.2345,-0.6789,1.0987,23.45,-67.89,109.87,25.5
3,1732800653123,0.3456,-0.7890,1.2098,34.56,-78.90,120.98,25.6
4,1732800653623,0.4567,-0.8901,1.3209,45.67,-89.01,132.09,25.7
5,1732800654123,0.5678,-0.9012,1.4320,56.78,-90.12,143.20,25.8
6,1732800654623,0.6789,-1.0123,1.5431,67.89,-101.23,154.31,25.9
```

## 🖥️ نمونه خروجی ترمینال

```
============================================================
🎯 ایجاد دیتاست MPU6050
============================================================
📊 تعداد نمونه‌ها: 5
⏱️  مدت هر نمونه: 3 ثانیه
🔄 فاصله بین نمونه‌ها: 2 ثانیه
📁 نام پایه فایل‌ها: dataset_sample
============================================================

🔄 آماده‌سازی نمونه 1/5

📊 شروع جمع‌آوری نمونه 1
📁 فایل: dataset_sample_001_20241128_143052.csv
⏱️  مدت زمان: 3 ثانیه (هر 0.5 ثانیه یک داده)
--------------------------------------------------
📈 داده 01/06 | شتاب: (+0.123, -0.456, +0.987) | باقی‌مانده: 2.5s
📈 داده 02/06 | شتاب: (+0.234, -0.567, +1.098) | باقی‌مانده: 2.0s
📈 داده 03/06 | شتاب: (+0.345, -0.678, +1.209) | باقی‌مانده: 1.5s
📈 داده 04/06 | شتاب: (+0.456, -0.789, +1.320) | باقی‌مانده: 1.0s
📈 داده 05/06 | شتاب: (+0.567, -0.890, +1.431) | باقی‌مانده: 0.5s
📈 داده 06/06 | شتاب: (+0.678, -0.901, +1.542) | باقی‌مانده: 0.0s
✅ نمونه 1 کامل شد - 6 داده ذخیره شد

😴 استراحت 2 ثانیه...

🔄 آماده‌سازی نمونه 2/5
...
```

## 🧪 تست سیستم

```python
# تست روی کامپیوتر (بدون سخت‌افزار)
python test_dataset_creator.py

# تست روی ESP32
from test_dataset_creator import test_dataset_creator
test_dataset_creator()
```

## 🔧 عیب‌یابی

### مشکلات رایج:

1. **خطای حافظه**: 
   - کاهش `num_samples`
   - افزایش `rest_duration`

2. **خطای I2C**:
   - بررسی اتصالات سیم‌کشی
   - بررسی تغذیه سنسور

3. **خطای فایل**:
   - بررسی فضای ذخیره‌سازی
   - بررسی مجوزهای نوشتن

### نکات بهینه‌سازی:

- برای دیتاست‌های بزرگ، از `rest_duration` بیشتر استفاده کنید
- فایل‌ها را به صورت دوره‌ای از ESP32 کپی کنید
- از نام‌های کوتاه‌تر برای `base_filename` استفاده کنید

## 📊 تحلیل داده‌ها

پس از ایجاد دیتاست، می‌توانید:

1. **فایل‌ها را در Excel باز کنید**
2. **با Python تحلیل کنید**:
   ```python
   import pandas as pd
   df = pd.read_csv('dataset_sample_001_20241128_143052.csv')
   print(df.describe())
   ```
3. **نمودارهای زمانی ایجاد کنید**
4. **الگوهای حرکتی را تحلیل کنید**

## 💡 کاربردهای دیتاست

- 🤖 آموزش مدل‌های یادگیری ماشین
- 📊 تحلیل الگوهای حرکتی
- 🔬 تحقیقات علمی
- 🎮 کنترل بازی‌ها
- 🏃‍♂️ تشخیص فعالیت‌ها

---

**موفق باشید! 🎉**
