#!/usr/bin/env python3
"""
تست سیستم پیشرفته MPU6050
آزمایش فیلترهای نویز و محاسبه زوایا
"""

import os
import time
from dataset_creator import DatasetCreator

def test_enhanced_features():
    """تست ویژگی‌های پیشرفته سیستم"""
    print("=" * 70)
    print("🧪 تست سیستم پیشرفته MPU6050")
    print("=" * 70)
    print("✨ ویژگی‌های تست:")
    print("   📊 داده‌های شتاب در 3 محور (X, Y, Z)")
    print("   🔄 داده‌های ژایروسکوپ در 3 محور (X, Y, Z)")
    print("   📐 زوایای محاسبه شده (Roll, Pitch, Yaw)")
    print("   🔧 فیلترهای پیشرفته نویز")
    print("   ⏱️  6 نمونه در هر 3 ثانیه")
    print("=" * 70)
    
    # ایجاد دیتاست تست
    creator = DatasetCreator("test_enhanced_system")
    
    try:
        # ایجاد 3 نمونه با فاصله 1 ثانیه
        print("🚀 شروع ایجاد دیتاست تست...")
        files = creator.create_dataset(num_samples=3, rest_duration=1)
        
        print("\n📋 تجزیه و تحلیل فایل‌های ایجاد شده:")
        print("-" * 70)
        
        for i, filename in enumerate(files, 1):
            print(f"\n📁 فایل {i}: {os.path.basename(filename)}")
            
            # خواندن و تجزیه فایل
            with open(filename, 'r') as f:
                lines = f.readlines()
            
            print(f"   📊 تعداد کل خطوط: {len(lines)}")
            print(f"   📈 تعداد داده‌ها: {len(lines) - 1}")  # منهای header
            
            # نمایش header
            if lines:
                header = lines[0].strip().split(',')
                print(f"   📋 ستون‌ها ({len(header)}):")
                for j, col in enumerate(header):
                    print(f"      {j+1:2d}. {col}")
            
            # نمایش نمونه داده‌ها
            if len(lines) > 1:
                print(f"   🔍 نمونه داده اول:")
                first_data = lines[1].strip().split(',')
                for j, (col, val) in enumerate(zip(header, first_data)):
                    if j < 3:  # فقط 3 ستون اول
                        print(f"      {col}: {val}")
                    elif j == 3:
                        print(f"      ... و {len(header)-3} ستون دیگر")
                        break
            
            # بررسی وجود زوایا
            if 'angle_x' in header:
                print("   ✅ زوایای محاسبه شده موجود است")
            else:
                print("   ❌ زوایای محاسبه شده موجود نیست")
            
            # بررسی تعداد داده‌ها
            expected_samples = 6
            actual_samples = len(lines) - 1
            if actual_samples == expected_samples:
                print(f"   ✅ تعداد داده‌ها صحیح است ({actual_samples}/{expected_samples})")
            else:
                print(f"   ❌ تعداد داده‌ها نادرست است ({actual_samples}/{expected_samples})")
        
        print("\n" + "=" * 70)
        print("📊 خلاصه تست:")
        print(f"   ✅ فایل‌های ایجاد شده: {len(files)}")
        print(f"   📈 مجموع داده‌ها: {(len(lines) - 1) * len(files) if lines else 0}")
        print("   🎯 ویژگی‌های تست شده:")
        print("      ✓ شتاب 3 محور")
        print("      ✓ ژایروسکوپ 3 محور") 
        print("      ✓ زوایای محاسبه شده")
        print("      ✓ فیلترهای نویز")
        print("      ✓ تایمینگ 6 نمونه در 3 ثانیه")
        
        # تست محتوای یک فایل به صورت کامل
        if files:
            print(f"\n🔍 محتوای کامل فایل اول:")
            print("-" * 70)
            with open(files[0], 'r') as f:
                content = f.read()
                print(content)
        
        print("=" * 70)
        print("🎉 تست با موفقیت کامل شد!")
        
        # پاک کردن فایل‌های تست
        cleanup = input("\n❓ آیا فایل‌های تست پاک شوند؟ (y/n): ").lower().strip()
        if cleanup in ['y', 'yes', 'بله', '']:
            print("\n🧹 پاک کردن فایل‌های تست...")
            for filename in files:
                try:
                    os.remove(filename)
                    print(f"   🗑️  {os.path.basename(filename)} پاک شد")
                except Exception as e:
                    print(f"   ❌ خطا در پاک کردن {filename}: {e}")
            print("✅ پاک‌سازی کامل شد")
        else:
            print("📁 فایل‌های تست حفظ شدند")
            for filename in files:
                print(f"   📄 {filename}")
    
    except Exception as e:
        print(f"\n❌ خطا در تست: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n👋 پایان تست")

def test_filter_performance():
    """تست عملکرد فیلترها"""
    print("\n" + "=" * 70)
    print("🔧 تست عملکرد فیلترها")
    print("=" * 70)
    
    try:
        from sensor_filters import LowPassFilter, MovingAverageFilter, NoiseThresholdFilter
        
        # تست فیلتر پایین‌گذر
        print("📊 تست فیلتر پایین‌گذر:")
        lpf = LowPassFilter(alpha=0.8)
        test_values = [1.0, 1.5, 0.8, 1.2, 0.9, 1.1]
        filtered_values = []
        
        for val in test_values:
            filtered = lpf.filter(val)
            filtered_values.append(filtered)
            print(f"   ورودی: {val:5.2f} → خروجی: {filtered:5.2f}")
        
        # تست فیلتر میانگین متحرک
        print("\n📊 تست فیلتر میانگین متحرک:")
        maf = MovingAverageFilter(window_size=3)
        
        for val in test_values:
            filtered = maf.filter(val)
            print(f"   ورودی: {val:5.2f} → میانگین: {filtered:5.2f}")
        
        # تست فیلتر آستانه نویز
        print("\n📊 تست فیلتر آستانه نویز:")
        ntf = NoiseThresholdFilter(threshold=0.1)
        noise_values = [0.05, 0.15, 0.02, 0.25, 0.08, 0.3]
        
        for val in noise_values:
            filtered = ntf.filter(val)
            status = "حذف شد" if filtered == 0 else "حفظ شد"
            print(f"   ورودی: {val:5.2f} → خروجی: {filtered:5.2f} ({status})")
        
        print("✅ تست فیلترها موفقیت‌آمیز بود")
        
    except ImportError:
        print("⚠️  ماژول فیلترها در دسترس نیست (حالت MicroPython)")
    except Exception as e:
        print(f"❌ خطا در تست فیلترها: {e}")

if __name__ == "__main__":
    # اجرای تست‌های اصلی
    test_enhanced_features()
    
    # تست فیلترها (در صورت امکان)
    test_filter_performance()
