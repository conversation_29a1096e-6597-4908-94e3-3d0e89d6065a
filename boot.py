"""
فایل boot.py برای ESP32
این فایل به طور خودکار هنگام روشن شدن ESP32 اجرا می‌شود
"""

import gc
import time

# آزادسازی حافظه در ابتدا
gc.collect()

print("=" * 50)
print("ESP32 MPU6050 Data Logger")
print("=" * 50)

# تاخیر کوتاه برای آماده شدن سیستم
time.sleep_ms(2000)

print("سیستم آماده است.")
print("برای شروع لاگ داده‌ها:")
print(">>> from mpu6050_csv_logger import main")
print(">>> main()")
print("-" * 50)
