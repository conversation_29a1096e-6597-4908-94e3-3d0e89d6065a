

from machine import I2C, Pin

import network
import time
import json
import urandom
from machine import I2C, Pin
import mpu6050
from umqtt.simple import MQTTClient

# --- Configuration ---
# Please create a config.py file with your WiFi credentials
# Example config.py:
# WIFI_SSID = "YourWiFiSSID"
# WIFI_PASSWORD = "YourWiFiPassword"
try:
    import config
except ImportError:
    print("Error: config.py not found. Please create it with your WiFi credentials.")
    # Use placeholders if config.py is not found
    class config:
        WIFI_SSID = "YOUR_WIFI_SSID"
        WIFI_PASSWORD = "YOUR_WIFI_PASSWORD"


# I2C and MQTT Configuration
I2C_SCL_PIN = 22
I2C_SDA_PIN = 21
MQTT_BROKER = "broker.mqttdashboard.com"
MQTT_TOPIC = b"micropython/test/mpu6050"
CLIENT_ID = str(urandom.getrandbits(32))
LOOP_DELAY_MS = 20


def connect_wifi(ssid, password):
    """
    Connects the device to a Wi-Fi network.

    Args:
        ssid (str): The SSID of the Wi-Fi network.
        password (str): The password for the Wi-Fi network.
    """
    station = network.WLAN(network.STA_IF)
    station.active(True)
    if not station.isconnected():
        print(f"Connecting to network '{ssid}'...")
        station.connect(ssid, password)
        while not station.isconnected():
            time.sleep(1)
    print("Network connection successful")
    print(station.ifconfig())


def run_sensor_stream():
    """
    Initializes the sensor and MQTT client, then enters a loop
    to read sensor data and publish it.
    """
    # Initialize I2C communication with the MPU6050 sensor
    print("Initializing I2C and MPU6050 sensor...")
    i2c = I2C(scl=Pin(I2C_SCL_PIN), sda=Pin(I2C_SDA_PIN))
    mpu = mpu6050.accel(i2c)
    print("Sensor initialized.")

    # Initialize and connect the MQTT client
    print(f"Connecting to MQTT broker at {MQTT_BROKER}...")
    mqtt_client = MQTTClient(CLIENT_ID, MQTT_BROKER)
    mqtt_client.connect()
    print("MQTT client connected.")

    # Main loop to read and publish data
    print("Starting to stream sensor data...")
    while True:
        try:
            # Get sensor values from the MPU6050
            values = mpu.get_values()

            # Publish the sensor values to the MQTT topic as a JSON string
            mqtt_client.publish(MQTT_TOPIC, json.dumps(values))

            # Print values to the console for local debugging
            print(values)

            # Wait for the specified delay before the next reading
            time.sleep_ms(LOOP_DELAY_MS)

        except OSError as e:
            print(f"An error occurred: {e}")
            # In case of an error (e.g., I2C issue), wait before retrying
            time.sleep(5)


def main():
    """
    Main function to set up the device and start the sensor stream.
    """
    # A small delay at the start can help with stability on some boards
    time.sleep_ms(1000)

    # Connect to the WiFi network
    connect_wifi(config.WIFI_SSID, config.WIFI_PASSWORD)

    # Start the main process of streaming sensor data
    run_sensor_stream()


ifimport network
import time
import json
import urandom
from machine import I2C, Pin
import mpu6050
from umqtt.simple import MQTTClient

# --- Configuration ---
# Please create a config.py file with your WiFi credentials
# Example config.py:
# WIFI_SSID = "YourWiFiSSID"
# WIFI_PASSWORD = "YourWiFiPassword"
try:
    import config
except ImportError:
    print("Error: config.py not found. Please create it with your WiFi credentials.")
    # Use placeholders if config.py is not found
    class config:
        WIFI_SSID = "DangerZone!"
        WIFI_PASSWORD = "Fs@2082099"

# I2C and MQTT Configuration
I2C_SCL_PIN = 22
I2C_SDA_PIN = 21
MQTT_BROKER = "broker.mqttdashboard.com"
MQTT_TOPIC = b"micropython/test/mpu6050"
CLIENT_ID = str(urandom.getrandbits(32))
LOOP_DELAY_MS = 20


def connect_wifi(ssid, password):
    """
    Connects the device to a Wi-Fi network.

    Args:
        ssid (str): The SSID of the Wi-Fi network.
        password (str): The password for the Wi-Fi network.
    
    Returns:
        bool: True if connection successful, False otherwise.
    """
    station = network.WLAN(network.STA_IF)
    station.active(True)
    
    if not station.isconnected():
        print(f"Connecting to network '{ssid}'...")
        station.connect(ssid, password)
        
        # Wait for connection with timeout
        timeout = 10  # 10 seconds timeout
        while not station.isconnected() and timeout > 0:
            time.sleep(1)
            timeout -= 1
            
        if not station.isconnected():
            print("Failed to connect to WiFi")
            return False
            
    print("Network connection successful")
    print(station.ifconfig())
    return True


def initialize_sensor():
    """
    Initializes the I2C communication and MPU6050 sensor.
    
    Returns:
        mpu6050.accel: Initialized sensor object.
    
    Raises:
        OSError: If sensor initialization fails.
    """
    print("Initializing I2C and MPU6050 sensor...")
    i2c = I2C(scl=Pin(I2C_SCL_PIN), sda=Pin(I2C_SDA_PIN))
    mpu = mpu6050.accel(i2c)
    print("Sensor initialized successfully.")
    return mpu


def initialize_mqtt():
    """
    Initializes and connects the MQTT client.
    
    Returns:
        MQTTClient: Connected MQTT client object.
    
    Raises:
        OSError: If MQTT connection fails.
    """
    print(f"Connecting to MQTT broker at {MQTT_BROKER}...")
    mqtt_client = MQTTClient(CLIENT_ID, MQTT_BROKER)
    mqtt_client.connect()
    print("MQTT client connected successfully.")
    return mqtt_client


def run_sensor_stream():
    """
    Main function that initializes components and runs the sensor data streaming loop.
    Continuously reads sensor data and publishes it to MQTT broker.
    """
    try:
        # Initialize sensor and MQTT client
        mpu = initialize_sensor()
        mqtt_client = initialize_mqtt()
        
        # Main streaming loop
        print("Starting to stream sensor data...")
        while True:
            try:
                # Get sensor values from the MPU6050
                values = mpu.get_values()

                # Publish the sensor values to the MQTT topic as a JSON string
                mqtt_client.publish(MQTT_TOPIC, json.dumps(values))

                # Print values to the console for local debugging
                print(values)

                # Wait for the specified delay before the next reading
                time.sleep_ms(LOOP_DELAY_MS)

            except OSError as e:
                print(f"Sensor reading error: {e}")
                # In case of an error (e.g., I2C issue), wait before retrying
                time.sleep(2)
                
    except Exception as e:
        print(f"Critical error in sensor stream: {e}")
        # Try to restart after a longer delay
        time.sleep(10)
        run_sensor_stream()


def main():
    """
    Main function to set up the device and start the sensor stream.
    Handles WiFi connection and starts the main sensor streaming process.
    """
    # Initial delay for system stability
    time.sleep_ms(1000)
    
    # Connect to WiFi network
    if not connect_wifi(config.WIFI_SSID, config.WIFI_PASSWORD):
        print("Cannot proceed without WiFi connection. Restarting...")
        time.sleep(5)
        return main()  # Retry
    
    # Start the main sensor streaming process
    run_sensor_stream()


if __name__ == "__main__":
    main()# WiFi Configuration
# Replace these values with your actual WiFi credentials

WIFI_SSID = "YourWiFiSSID"
WIFI_PASSWORD = "YourWiFiPassword"

# Optional: You can add other configuration parameters here
# MQTT_BROKER = "your-custom-broker.com"
# MQTT_PORT = 1883 __name__ == "__main__":
    main()))
mpu = mpu6050.accel(i2c)
c = MQTTClient(str(urandom.getrandbits(32)), "broker.mqttdashboard.com")
c.connect()


while True:
    values = mpu.get_values()
    c.publish(b"micropython/test/mpu6050",
            json.dumps(values))
    print(values)
    time.sleep_ms(20)