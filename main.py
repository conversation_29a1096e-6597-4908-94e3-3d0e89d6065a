"""
main.py file for ESP32
This file runs after boot.py and starts the logging system
"""

import time

def auto_start():
    """Automatic start of logging system"""
    print("Starting automatic logging system...")
    print("Press Ctrl+C to cancel")
    
    # 5 second delay to allow cancellation
    for i in range(5, 0, -1):
        print(f"Starting in {i} seconds...")
        time.sleep_ms(1000)
    
    try:
        from mpu6050_csv_logger import main
        main()
    except KeyboardInterrupt:
        print("Logging system stopped")
    except Exception as e:
        print(f"Error running logging system: {e}")

if __name__ == "__main__":
    auto_start()
