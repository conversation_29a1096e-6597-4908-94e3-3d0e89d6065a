"""DO NOT EDIT.

This file was autogenerated. Do not edit it by hand,
since your modifications would be overwritten.
"""

from keras.optimizers import legacy as legacy
from keras.optimizers import schedules as schedules
from keras.src.optimizers import deserialize as deserialize
from keras.src.optimizers import get as get
from keras.src.optimizers import serialize as serialize
from keras.src.optimizers.adadelta import <PERSON><PERSON><PERSON> as <PERSON>del<PERSON>
from keras.src.optimizers.adafactor import Adafactor as Adafactor
from keras.src.optimizers.adagrad import Adagrad as Adagrad
from keras.src.optimizers.adam import <PERSON> as <PERSON>
from keras.src.optimizers.adamax import <PERSON><PERSON> as Adamax
from keras.src.optimizers.adamw import AdamW as AdamW
from keras.src.optimizers.ftrl import Ftrl as Ftrl
from keras.src.optimizers.lamb import Lamb as Lamb
from keras.src.optimizers.lion import Lion as Lion
from keras.src.optimizers.loss_scale_optimizer import (
    LossScaleOptimizer as LossScaleOptimizer,
)
from keras.src.optimizers.muon import Muon as <PERSON>on
from keras.src.optimizers.nadam import Nadam as Nadam
from keras.src.optimizers.optimizer import Optimizer as Optimizer
from keras.src.optimizers.rmsprop import RMSprop as RMSprop
from keras.src.optimizers.sgd import SGD as SGD
