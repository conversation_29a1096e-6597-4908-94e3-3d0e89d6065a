{
    "workbench.colorCustomizations": {
        "[*]": {
            // تنظیمات ویرایشگر
            "editor.background": "#01050c",  // پس‌زمینه تیره با ته‌رنگ آبی
            "editor.lineHighlightBackground": "#151530",  // رنگ هایلایت خط فعلی
            "editor.selectionBackground": "#293047",  // رنگ انتخاب متن - آبی تیره
            "editorGutter.background": "#01050e",  // رنگ پس‌زمینه شماره خطوط
            "editor.findMatchBackground": "#4a208080",  // رنگ پس‌زمینه جستجو - بنفش با شفافیت
            "editor.findMatchHighlightBackground": "#2a408080",  // رنگ پس‌زمینه هایلایت جستجو - آبی با شفافیت

            // تنظیمات سایدبار
            "sideBar.background": "#000000",  // پس‌زمینه سایدبار هماهنگ با ویرایشگر
            "sideBar.foreground": "#ffffff",  // رنگ متن سایدبار - سفید
            "sideBar.border": "#06060a",  // رنگ مرز سایدبار
            "sideBarTitle.foreground": "#ffffff",  // رنگ عنوان سایدبار - سفید
            "sideBarSectionHeader.background": "#000004",  // پس‌زمینه هدر بخش‌های سایدبار
            "sideBarSectionHeader.foreground": "#b9d9ff",  // رنگ متن هدر بخش‌های سایدبار - قرمز
            "list.hoverBackground": "#0e0e1f",  // پس‌زمینه آیتم‌های لیست در حالت هاور
            "list.hoverForeground": "#64b8c7",  // رنگ متن آیتم‌های لیست در حالت هاور - قرمز
            "list.activeSelectionBackground": "#0f0f1e",  // پس‌زمینه آیتم انتخاب شده فعال
            "list.activeSelectionForeground": "#ff00bf",  // رنگ متن آیتم انتخاب شده فعال - قرمز
            "list.inactiveSelectionBackground": "#040430",  // پس‌زمینه آیتم انتخاب شده غیرفعال
            "list.inactiveSelectionForeground": "#ffffff",  // رنگ متن آیتم انتخاب شده غیرفعال - سفید

            // تنظیمات ترمینال
            "terminal.background": "#06060f",  // پس‌زمینه ترمینال هماهنگ با ویرایشگر
            "terminal.foreground": "#ffffff",  // رنگ متن ترمینال
            "terminal.tab.activeBorder": "#3c72e7",  // رنگ مرز تب فعال ترمینال - قرمز
            "terminalCursor.background": "#1A1A1A",  // رنگ پس‌زمینه مکان‌نما
            "terminalCursor.foreground": "#ffb0b0",  // رنگ مکان‌نما
            "terminal.border": "#151530",  // رنگ مرز ترمینال
            "terminalCommandDecoration.defaultBackground": "#536cfd",  // رنگ پس‌زمینه دکوراسیون دستور - قرمز
            "terminalCommandDecoration.successBackground": "#cdffec",  // رنگ پس‌زمینه دکوراسیون موفقیت
            "terminalCommandDecoration.errorBackground": "#3b64eb",  // رنگ پس‌زمینه دکوراسیون خطا
            "panel.background": "#01050e",  // پس‌زمینه پنل هماهنگ با ویرایشگر
            "panel.border": "#151530",  // رنگ مرز پنل
            "panelTitle.activeBorder": "#604eff",  // رنگ مرز عنوان فعال پنل - قرمز
            "panelTitle.activeForeground": "#ff0088",  // رنگ متن عنوان فعال پنل - قرمز
            "panelTitle.inactiveForeground": "#ffffff",  // رنگ متن عنوان غیرفعال پنل - سفید
            "panelInput.border": "#131320",  // رنگ مرز ورودی پنل
            "terminal.ansiBlack": "#000000",
            "terminal.ansiBlue": "#2472C8",
            "terminal.ansiBrightBlack": "#666666",
            "terminal.ansiBrightBlue": "#3B8EEA",
            "terminal.ansiBrightCyan": "#29B8DB",
            "terminal.ansiBrightGreen": "#23D18B",
            "terminal.ansiBrightMagenta": "#D670D6",
            "terminal.ansiBrightRed": "#4caff1",
            "terminal.ansiBrightWhite": "#E5E5E5",
            "terminal.ansiBrightYellow": "#F5F543",
            "terminal.ansiCyan": "#11A8CD",
            "terminal.ansiGreen": "#0DBC79",
            "terminal.ansiMagenta": "#BC3FBC",
            "terminal.ansiRed": "#4464f3",
            "terminal.ansiWhite": "#E5E5E5",
            "terminal.ansiYellow": "#E5E510",

            // تنظیمات نوار وضعیت
            "statusBar.background": "#007ACC",  // پس‌زمینه نوار وضعیت
            "statusBar.foreground": "#FFFFFF",  // رنگ متن نوار وضعیت
            "statusBar.noFolderBackground": "#68217A",  // پس‌زمینه نوار وضعیت وقتی پوشه‌ای باز نیست
            "statusBar.debuggingBackground": "#3363cc",  // پس‌زمینه نوار وضعیت در حالت دیباگ

            // تنظیمات نوار عنوان
            "titleBar.activeBackground": "#06060f",  // پس‌زمینه نوار عنوان فعال هماهنگ با ویرایشگر
            "titleBar.activeForeground": "#ff0000",  // رنگ متن نوار عنوان فعال - صورتی
            "titleBar.inactiveBackground": "#0a0e1a",  // پس‌زمینه نوار عنوان غیرفعال
            "titleBar.inactiveForeground": "#FFFFFF",  // رنگ متن نوار عنوان غیرفعال - سفید
            "titleBar.border": "#151530",  // رنگ مرز نوار عنوان

            // تنظیمات تب‌ها
            "tab.activeBackground": "#151530",  // پس‌زمینه تب فعال
            "tab.activeForeground": "#ffddf6",  // رنگ متن تب فعال - صورتی
            "tab.inactiveBackground": "#0a0a1a",  // پس‌زمینه تب غیرفعال
            "tab.inactiveForeground": "#FFFFFF",  // رنگ متن تب غیرفعال - سفید
            "tab.hoverBackground": "#202040",  // پس‌زمینه تب در حالت هاور
            "tab.hoverForeground": "#00ff80",  // رنگ متن تب در حالت هاور - صورتی
            "tab.activeBorder": "#ff00bf",  // رنگ مرز تب فعال - صورتی
            "tab.border": "#2b2b45",  // رنگ مرز تب‌ها

            // تنظیمات آیکون‌ها
            "icon.foreground": "#ff00bf",  // رنگ آیکون‌ها - صورتی
            "activityBar.foreground": "#ff00bf",  // رنگ آیکون‌های نوار فعالیت - صورتی
            "activityBar.inactiveForeground": "#FFFFFF",  // رنگ آیکون‌های غیرفعال نوار فعالیت - سفید
            "activityBar.background": "#070711",  // پس‌زمینه نوار فعالیت هماهنگ با ویرایشگر
            "activityBar.border": "#151530",  // رنگ مرز نوار فعالیت
            "activityBar.activeBackground": "#151530",  // پس‌زمینه آیکون فعال نوار فعالیت
            "activityBarBadge.background": "#ff0077",  // پس‌زمینه نشان نوار فعالیت - صورتی
            "activityBarBadge.foreground": "#FFFFFF",  // رنگ متن نشان نوار فعالیت - سفید
            "activityBar.activeBorder": "#fdcdf1",  // رنگ مرز آیکون فعال نوار فعالیت - صورتی

            // تنظیمات منوها
            "menu.background": "#0a0a1a",  // پس‌زمینه منو هماهنگ با ویرایشگر
            "menu.foreground": "#FFFFFF",  // رنگ متن منو - سفید
            "menu.selectionBackground": "#470272",  // پس‌زمینه انتخاب منو - صورتی
            "menu.selectionForeground": "#FFFFFF",  // رنگ متن انتخاب منو - سفید
            "menu.separatorBackground": "#151530",  // رنگ جداکننده منو
            "menubar.selectionBackground": "#151530",  // پس‌زمینه انتخاب نوار منو
            "menubar.selectionForeground": "#7cffe9",  // رنگ متن انتخاب نوار منو - صورتی
            "menubar.selectionBorder": "#7c00d4",  // رنگ مرز انتخاب نوار منو - صورتی

            // تنظیمات هاور برای سایر عناصر
            "button.hoverBackground": "#980373",  // پس‌زمینه دکمه در حالت هاور - صورتی
            "dropdown.foreground": "#FFFFFF",  // رنگ متن منوی کشویی - سفید
            "dropdown.background": "#0a0a1a",  // پس‌زمینه منوی کشویی هماهنگ با ویرایشگر
            "input.foreground": "#ffffff",  // رنگ متن ورودی - سفید
            "input.background": "#0a0a1a",  // پس‌زمینه ورودی هماهنگ با ویرایشگر
            "input.placeholderForeground": "#f2ebeb",  // رنگ متن پیش‌فرض ورودی
            "editorWidget.foreground": "#fff7f7",  // رنگ متن ویجت ویرایشگر - صورتی
            "editorWidget.background": "#0a0a1a",  // پس‌زمینه ویجت ویرایشگر هماهنگ با ویرایشگر
            "editorHoverWidget.foreground": "#FFFFFF",  // رنگ متن ویجت هاور ویرایشگر - سفید
            "editorHoverWidget.background": "#0a0a1a",  // پس‌زمینه ویجت هاور ویرایشگر هماهنگ با ویرایشگر
            "editorHoverWidget.border": "#ffefef"  // رنگ مرز ویجت هاور ویرایشگر - صورتی
        }
    },
    // color candy theme settings with happy colors
    "editor.tokenColorCustomizations": {
        "[*]": {
            "textMateRules": [
                {
                    "scope": [
                        "string.quoted.single",
                        "string.quoted.double",
                        "string.quoted.other",
                        "string.template",
                        "string.interpolated",
                        "string.regexp"
                    ],
                    "settings": {
                        "foreground": "#00ff00"  // رنگ سبز برای استرینگ‌ها
                    }
                },
                {
                    "scope": [
                        "string.quoted.triple",
                        "comment.block.documentation",
                        "comment.block.documentation.python"
                    ],
                    "settings": {
                        "foreground": "#86b1ee",  // رنگ خاکستری روشن برای داک استرینگ‌ها
                        "fontStyle": "italic"
                    }
                },
                {
                    "scope": [
                        "variable",
                        "variable.parameter",
                        "variable.other",
                        "variable.language"
                    ],
                    "settings": {
                        "foreground": "#ff0090"  // رنگ آبی روشن برای متغیرها
                    }
                },
                {
                    "scope": [
                        "variable.other.constant"
                    ],
                    "settings": {
                        "foreground": "#a600ff"  // رنگ آبی برای متغیرهای ثابت
                    }
                },
                {
                    "scope": [
                        "variable.other.readwrite.instance"
                    ],
                    "settings": {
                        "foreground": "#ff00b3"  // رنگ صورتی روشن برای متغیرهای نمونه
                    }
                },
                {
                    "scope": [
                        "entity.name.class",
                        "entity.name.type.class",
                        "entity.other.inherited-class",
                        "support.class"
                    ],
                    "settings": {
                        "foreground": "#ff2ff5"  // رنگ آلبالویی برای کلاس‌ها
                    }
                },
                {
                    "scope": [
                        "entity.name.function",
                        "support.function",
                        "meta.method.declaration",
                        "meta.function-call"
                    ],
                    "settings": {
                        "foreground": "#09f2d3"  // رنگ آلبالویی برای فراخوانی توابع و متدها
                    }
                },
                {
                    "scope": [
                        "comment",
                        "comment.line",
                        "comment.block"
                    ],
                    "settings": {
                        "foreground": "#fff8f8",  // رنگ سبز تیره برای کامنت‌ها
                        "fontStyle": "italic"
                    }
                },
                {
                    "scope": [
                        "keyword",
                        "keyword.control",
                        "keyword.operator",
                        "storage.type",
                        "storage.modifier"
                    ],
                    "settings": {
                        "foreground": "#00e6e6"  // رنگ آبی برای کلیدواژه‌ها
                    }
                },
                {
                    "scope": [
                        "constant",
                        "constant.numeric",
                        "constant.language",
                        "constant.character",
                        "constant.other"
                    ],
                    "settings": {
                        "foreground": "#ff4d00"  // رنگ سبز روشن برای ثابت‌ها و اعداد
                    }
                },
                {
                    "scope": [
                        "entity.name.tag",
                        "punctuation.definition.tag"
                    ],
                    "settings": {
                        "foreground": "#008cff"  // رنگ آبی برای تگ‌ها
                    }
                },
                {
                    "scope": [
                        "entity.name.namespace",
                        "entity.name.type.namespace"
                    ],
                    "settings": {
                        "foreground": "#0077ff"  // رنگ نارنجی برای فضای نام
                    }
                },
                {
                    "scope": [
                        "entity.other.attribute-name"
                    ],
                    "settings": {
                        "foreground": "#ffef0d"  // رنگ آبی روشن برای نام‌های ویژگی
                    }
                },
                {
                    "scope": [
                        "punctuation",
                        "meta.brace",
                        "meta.delimiter"
                    ],
                    "settings": {
                        "foreground": "#24ffaf"  // رنگ فیروزه‌ای روشن برای نقطه‌گذاری
                    }
                },
                {
                    "scope": [
                        "text",
                        "source"
                    ],
                    "settings": {
                        "foreground": "#9a9fa7"  // رنگ زرد طلایی برای متن عادی (به جای سفید)
                    }
                },
                {
                    "scope": [
                        "support.variable",
                        "support.other"
                    ],
                    "settings": {
                        "foreground": "#06ffff"  // رنگ فیروزه‌ای برای انواع پشتیبانی شده
                    }
                },
                {
                    "scope": [
                        "support.type",
                        "support.class",
                        "support.constant",
                    ],
                    "settings": {
                        "foreground": "#7860ff"  // رنگ فیروزه‌ای برای انواع پشتیبانی شده
                    }
                },
                {
                    "scope": [
                        "meta.decorator",
                        "meta.annotation"
                    ],
                    "settings": {
                        "foreground": "#ffffff"  // رنگ زرد برای دکوراتورها
                    }
                },
                {
                    "scope": [
                        "invalid",
                        "invalid.illegal",
                        "invalid.deprecated"
                    ],
                    "settings": {
                        "foreground": "#ff0909",  // رنگ قرمز روشن برای کد نامعتبر
                        "fontStyle": "italic underline"
                    }
                },
                {
                    "scope": [
                        "meta.parameter",
                        "variable.parameter"
                    ],
                    "settings": {
                        "foreground": "#05a184"  // رنگ نارنجی برای پارامترها
                    }
                },
                {
                    "scope": [
                        "meta.object-literal.key",
                        "meta.object-literal.key entity.name.function"
                    ],
                    "settings": {
                        "foreground": "#6ba6ff"  // رنگ آبی آسمانی برای کلیدهای شیء
                    }
                },
                {
                    "scope": [
                        "meta.property-name",
                        "variable.other.property"
                    ],
                    "settings": {
                        "foreground": "#48ff00"  // رنگ سبز فیروزه‌ای برای نام‌های ویژگی
                    }
                },
                {
                    "scope": [
                        "meta.preprocessor",
                        "entity.name.function.preprocessor"
                    ],
                    "settings": {
                        "foreground": "#ffffff"  // رنگ نارنجی تیره برای پیش‌پردازنده‌ها
                    }
                },
                {
                    "scope": [
                        "meta.diff",
                        "meta.diff.header"
                    ],
                    "settings": {
                        "foreground": "#0088ff"  // رنگ آبی روشن برای diff
                    }
                }
            ]
        }
    },
    "python-envs.pythonProjects": [],
    "codium.codeCompletion.enable": true,
    "trae.tab.enableRename": false,
    "trae.tab.enableAutoImport": false
}
