{"_comment": "فایل تنظیمات پروژه MPU6050 CSV Logger", "_description": "این فایل شامل تمام تنظیمات قابل تغییر سیستم است", "hardware": {"_comment": "تنظیمات سخت‌افزاری", "i2c_scl_pin": 22, "i2c_sda_pin": 21, "i2c_frequency": 400000, "mpu6050_address": 104}, "timing": {"_comment": "تنظیمات زمان‌بندی", "read_interval_ms": 500, "sample_duration_ms": 3000}, "csv": {"_comment": "تنظیمات فایل CSV", "filename": "mpu6050_data.csv", "buffer_size": 5}, "display": {"_comment": "تنظیمات نمایش", "show_console_output": true, "decimal_places": 4}, "memory": {"_comment": "تنظیمات بهینه‌سازی حافظه", "enable_garbage_collection": true, "gc_threshold": 10}, "sensor": {"_comment": "تنظیمات کالیبراسیون سنسور", "accel_scale": 16384.0, "gyro_scale": 131.0}, "dataset": {"_comment": "تنظیمات ایجاد دیتاست", "default_samples": 10, "default_rest_duration": 2, "samples_per_collection": 6}}