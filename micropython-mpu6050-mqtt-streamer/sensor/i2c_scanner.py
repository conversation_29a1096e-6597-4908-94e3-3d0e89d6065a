# This script scans the I2C bus to find connected devices.

from machine import I2C, Pin

# --- Configuration ---
# Configure the I2C pins. Adjust these to match your board's default I2C pins if needed.
# Common pins for ESP8266: scl=Pin(5), sda=Pin(4)
# Common pins for ESP32: scl=Pin(22), sda=Pin(21)
i2c = I2C(scl=Pin(22), sda=Pin(21))

# --- I2C Device Scanning ---
print('Scanning I2C bus...')
devices = i2c.scan()

if len(devices) == 0:
    print("No I2C devices found.")
else:
    print('I2C devices found:', len(devices))
    for device in devices:
        print("Decimal address: ", device, " | Hex address: ", hex(device))
