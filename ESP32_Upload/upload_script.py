#!/usr/bin/env python3
"""
اسکریپت خودکار آپلود فایل‌ها روی ESP32
استفاده از ampy برای آپلود همه فایل‌های ضروری
"""

import os
import sys
import subprocess
import time
import argparse

def check_ampy():
    """بررسی نصب بودن ampy"""
    try:
        result = subprocess.run(['ampy', '--version'], capture_output=True, text=True)
        return True
    except FileNotFoundError:
        return False

def install_ampy():
    """نصب ampy"""
    print("📦 در حال نصب ampy...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'adafruit-ampy'], check=True)
        print("✅ ampy با موفقیت نصب شد")
        return True
    except subprocess.CalledProcessError:
        print("❌ خطا در نصب ampy")
        return False

def upload_file(port, filename):
    """آپلود یک فایل"""
    try:
        print(f"📤 آپلود {filename}...")
        result = subprocess.run(['ampy', '--port', port, 'put', filename], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print(f"✅ {filename} آپلود شد")
            return True
        else:
            print(f"❌ خطا در آپلود {filename}: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ تایم‌اوت در آپلود {filename}")
        return False
    except Exception as e:
        print(f"❌ خطا در آپلود {filename}: {e}")
        return False

def list_esp32_files(port):
    """لیست فایل‌های روی ESP32"""
    try:
        result = subprocess.run(['ampy', '--port', port, 'ls'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            return result.stdout.strip().split('\n')
        else:
            return []
    except:
        return []

def main():
    """تابع اصلی"""
    parser = argparse.ArgumentParser(description='آپلود خودکار فایل‌ها روی ESP32')
    parser.add_argument('--port', '-p', required=True, help='پورت سریال (مثل COM3 یا /dev/ttyUSB0)')
    parser.add_argument('--check-only', '-c', action='store_true', help='فقط بررسی فایل‌ها')
    parser.add_argument('--force', '-f', action='store_true', help='آپلود اجباری (بدون تایید)')
    
    args = parser.parse_args()
    
    print("🚀 اسکریپت آپلود ESP32")
    print("=" * 50)
    
    # بررسی ampy
    if not check_ampy():
        print("❌ ampy نصب نیست")
        if input("📦 آیا می‌خواهید ampy را نصب کنید؟ (y/n): ").lower() == 'y':
            if not install_ampy():
                return
        else:
            print("💡 برای نصب دستی: pip install adafruit-ampy")
            return
    
    # فایل‌های مورد نیاز
    required_files = [
        'config.json',
        'config_loader.py',
        'sensor_filters.py',
        'mpu6050_enhanced.py',
        'mpu6050_serial.py',
        'dataset_creator.py',
        'main.py'
    ]
    
    # بررسی وجود فایل‌ها
    missing_files = []
    for filename in required_files:
        if not os.path.exists(filename):
            missing_files.append(filename)
    
    if missing_files:
        print(f"❌ فایل‌های گمشده: {missing_files}")
        return
    
    print(f"✅ همه {len(required_files)} فایل موجود است")
    
    # محاسبه حجم کل
    total_size = sum(os.path.getsize(f) for f in required_files)
    print(f"💾 حجم کل: {total_size:,} bytes ({total_size/1024:.1f} KB)")
    
    if args.check_only:
        print("🔍 حالت بررسی - آپلود انجام نمی‌شود")
        return
    
    # بررسی اتصال ESP32
    print(f"\n🔌 بررسی اتصال به {args.port}...")
    esp32_files = list_esp32_files(args.port)
    
    if not esp32_files:
        print("❌ اتصال به ESP32 برقرار نیست")
        print("🔧 بررسی کنید:")
        print("   - کابل USB متصل باشد")
        print("   - درایور ESP32 نصب باشد")
        print("   - پورت صحیح باشد")
        return
    
    print(f"✅ اتصال برقرار - {len(esp32_files)} فایل روی ESP32")
    
    # نمایش فایل‌های موجود
    if esp32_files and esp32_files[0]:
        print("📁 فایل‌های موجود روی ESP32:")
        for f in esp32_files:
            if f.strip():
                print(f"   - {f}")
    
    # تایید آپلود
    if not args.force:
        print(f"\n📤 آماده آپلود {len(required_files)} فایل:")
        for i, f in enumerate(required_files, 1):
            size = os.path.getsize(f)
            print(f"   {i}. {f} ({size} bytes)")
        
        confirm = input(f"\n❓ آیا می‌خواهید آپلود را شروع کنید؟ (y/n): ")
        if confirm.lower() != 'y':
            print("⏹️  آپلود لغو شد")
            return
    
    # شروع آپلود
    print(f"\n🚀 شروع آپلود...")
    start_time = time.time()
    
    success_count = 0
    failed_files = []
    
    for i, filename in enumerate(required_files, 1):
        print(f"\n[{i}/{len(required_files)}]", end=" ")
        
        if upload_file(args.port, filename):
            success_count += 1
            time.sleep(0.5)  # کمی تاخیر بین آپلودها
        else:
            failed_files.append(filename)
    
    # خلاصه نتایج
    elapsed = time.time() - start_time
    print(f"\n" + "=" * 50)
    print(f"📊 خلاصه آپلود:")
    print(f"   ⏱️  زمان: {elapsed:.1f} ثانیه")
    print(f"   ✅ موفق: {success_count}/{len(required_files)}")
    print(f"   ❌ ناموفق: {len(failed_files)}")
    
    if failed_files:
        print(f"   🔴 فایل‌های ناموفق: {failed_files}")
        print("\n🔧 برای حل مشکل:")
        print("   - ESP32 را reset کنید")
        print("   - دوباره تلاش کنید")
    else:
        print(f"\n🎉 همه فایل‌ها با موفقیت آپلود شدند!")
        print("🚀 ESP32 آماده استفاده است")
        print("\n📡 برای تست:")
        print(f"   python serial_reader.py --port {args.port} --duration 30")

if __name__ == "__main__":
    # مثال استفاده:
    # python upload_script.py --port COM3
    # python upload_script.py --port /dev/ttyUSB0 --force
    # python upload_script.py --port COM3 --check-only
    
    main()
