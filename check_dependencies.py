#!/usr/bin/env python3
"""
بررسی وابستگی‌های فایل‌ها برای آپلود روی ESP32
"""

import os
import ast

def check_file_imports(filename):
    """بررسی import های یک فایل"""
    if not os.path.exists(filename):
        return f"❌ فایل {filename} وجود ندارد"
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content)
        imports = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    for alias in node.names:
                        imports.append(f"{node.module}.{alias.name}")
        
        return imports
    except Exception as e:
        return f"❌ خطا در پردازش {filename}: {e}"

def main():
    """بررسی اصلی"""
    print("🔍 بررسی وابستگی‌های فایل‌ها برای ESP32")
    print("=" * 60)
    
    # فایل‌های مورد بررسی
    files_to_check = [
        'config_loader.py',
        'sensor_filters.py', 
        'mpu6050_enhanced.py',
        'mpu6050_serial.py',
        'dataset_creator.py'
    ]
    
    # فایل‌های استاندارد MicroPython
    micropython_modules = {
        'machine', 'time', 'gc', 'os', 'ujson', 'math'
    }
    
    # فایل‌های پروژه
    project_files = {
        'config_loader', 'sensor_filters', 'mpu6050_enhanced', 
        'mpu6050_csv_logger', 'dataset_creator'
    }
    
    all_dependencies = {}
    
    for filename in files_to_check:
        print(f"\n📁 بررسی {filename}:")
        imports = check_file_imports(filename)
        
        if isinstance(imports, str):  # خطا
            print(f"   {imports}")
            continue
        
        all_dependencies[filename] = imports
        
        # تجزیه وابستگی‌ها
        micropython_deps = []
        project_deps = []
        external_deps = []
        
        for imp in imports:
            base_module = imp.split('.')[0]
            if base_module in micropython_modules:
                micropython_deps.append(imp)
            elif base_module in project_files:
                project_deps.append(imp)
            else:
                external_deps.append(imp)
        
        print(f"   ✅ MicroPython: {micropython_deps}")
        print(f"   📦 پروژه: {project_deps}")
        if external_deps:
            print(f"   ⚠️  خارجی: {external_deps}")
    
    print("\n" + "=" * 60)
    print("📋 خلاصه وابستگی‌ها:")
    
    # بررسی فایل‌های ضروری
    required_files = ['config.json'] + files_to_check
    
    print(f"\n📤 فایل‌های ضروری برای آپلود ({len(required_files)}):")
    for i, filename in enumerate(required_files, 1):
        status = "✅" if os.path.exists(filename) else "❌"
        size = f"({os.path.getsize(filename)} bytes)" if os.path.exists(filename) else ""
        print(f"   {i:2d}. {status} {filename} {size}")
    
    # بررسی وابستگی‌های دایره‌ای
    print(f"\n🔄 بررسی وابستگی‌های دایره‌ای:")
    circular_deps = []
    
    for file1, deps1 in all_dependencies.items():
        for file2, deps2 in all_dependencies.items():
            if file1 != file2:
                base1 = file1.replace('.py', '')
                base2 = file2.replace('.py', '')
                
                if any(base2 in dep for dep in deps1) and any(base1 in dep for dep in deps2):
                    circular_deps.append((file1, file2))
    
    if circular_deps:
        print("   ⚠️  وابستگی‌های دایره‌ای یافت شد:")
        for dep in circular_deps:
            print(f"      {dep[0]} ↔ {dep[1]}")
    else:
        print("   ✅ وابستگی دایره‌ای یافت نشد")
    
    # ترتیب آپلود پیشنهادی
    print(f"\n📤 ترتیب پیشنهادی آپلود:")
    upload_order = [
        'config.json',
        'config_loader.py',
        'sensor_filters.py',
        'mpu6050_enhanced.py',
        'mpu6050_serial.py',
        'dataset_creator.py'
    ]
    
    for i, filename in enumerate(upload_order, 1):
        status = "✅" if os.path.exists(filename) else "❌"
        print(f"   {i}. {status} {filename}")
    
    # بررسی حجم کل
    total_size = 0
    for filename in upload_order:
        if os.path.exists(filename):
            total_size += os.path.getsize(filename)
    
    print(f"\n💾 حجم کل فایل‌ها: {total_size:,} bytes ({total_size/1024:.1f} KB)")
    
    if total_size > 100000:  # 100KB
        print("   ⚠️  حجم زیاد - ممکن است روی ESP32 مشکل ایجاد کند")
    else:
        print("   ✅ حجم مناسب برای ESP32")
    
    print("\n🎯 نتیجه:")
    missing_files = [f for f in required_files if not os.path.exists(f)]
    if missing_files:
        print(f"   ❌ فایل‌های گمشده: {missing_files}")
        print("   🔧 ابتدا فایل‌های گمشده را ایجاد کنید")
    else:
        print("   ✅ همه فایل‌های ضروری موجود است")
        print("   🚀 آماده آپلود روی ESP32")

if __name__ == "__main__":
    main()
