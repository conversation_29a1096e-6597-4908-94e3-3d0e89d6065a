"""
فایل تست برای سیستم MPU6050 CSV Logger
این فایل برای تست عملکرد کد بدون نیاز به سخت‌افزار واقعی استفاده می‌شود
"""

import time
import random

class MockI2C:
    """کلاس شبیه‌ساز I2C برای تست"""
    
    def __init__(self, scl, sda, freq):
        self.scl = scl
        self.sda = sda
        self.freq = freq
        print(f"Mock I2C initialized: SCL={scl}, SDA={sda}, FREQ={freq}")
    
    def writeto(self, addr, data):
        """شبیه‌سازی نوشتن داده"""
        print(f"Mock I2C write to {hex(addr)}: {list(data)}")
    
    def readfrom_mem(self, addr, reg, length):
        """شبیه‌ساز<PERSON> خواندن داده از حافظه"""
        # تولید داده‌های تصادفی شبیه به MPU6050
        mock_data = bytearray(14)
        
        # شتاب‌سنج (6 بایت اول)
        for i in range(0, 6, 2):
            # تولید مقدار تصادفی بین -2g تا +2g
            value = int(random.uniform(-32768, 32767))
            mock_data[i] = (value >> 8) & 0xFF
            mock_data[i + 1] = value & 0xFF
        
        # دما (2 بایت)
        temp_value = int(random.uniform(20, 30) * 340 - 36.53 * 340)
        mock_data[6] = (temp_value >> 8) & 0xFF
        mock_data[7] = temp_value & 0xFF
        
        # ژایروسکوپ (6 بایت آخر)
        for i in range(8, 14, 2):
            # تولید مقدار تصادفی برای زاویه
            value = int(random.uniform(-32768, 32767))
            mock_data[i] = (value >> 8) & 0xFF
            mock_data[i + 1] = value & 0xFF
        
        return mock_data

class MockPin:
    """کلاس شبیه‌ساز Pin برای تست"""
    
    def __init__(self, pin_number):
        self.pin_number = pin_number
        print(f"Mock Pin {pin_number} initialized")

def test_mpu6050_logger():
    """تست عملکرد کامل سیستم لاگر"""
    print("=" * 60)
    print("شروع تست سیستم MPU6050 CSV Logger")
    print("=" * 60)

    # وارد کردن ماژول‌های اصلی
    try:
        # جایگزینی ماژول‌های سخت‌افزاری با شبیه‌ساز
        import sys

        # ایجاد ماژول شبیه‌ساز machine
        class MockMachine:
            I2C = MockI2C
            Pin = MockPin

        sys.modules['machine'] = MockMachine()

        # شبیه‌سازی تابع‌های MicroPython در ماژول time
        import time as original_time

        class MockTime:
            sleep = original_time.sleep
            time = original_time.time

            @staticmethod
            def sleep_ms(ms):
                original_time.sleep(ms / 1000.0)

            @staticmethod
            def ticks_ms():
                return int(original_time.time() * 1000)

            @staticmethod
            def ticks_diff(a, b):
                return a - b

        sys.modules['time'] = MockTime()

        # وارد کردن کد اصلی
        from mpu6050_csv_logger import MPU6050, CSVLogger
        import config

        print("ماژول‌ها با موفقیت وارد شدند")

    except ImportError as e:
        print(f"خطا در وارد کردن ماژول‌ها: {e}")
        return False
    
    # تست کلاس MPU6050
    print("\n--- تست کلاس MPU6050 ---")
    try:
        mock_i2c = MockI2C(scl=MockPin(22), sda=MockPin(21), freq=400000)
        mpu = MPU6050(mock_i2c)
        
        # تست خواندن داده
        for i in range(3):
            data = mpu.get_sensor_data()
            if data:
                print(f"نمونه {i+1}: شتاب=({data['accel_x']:.3f}, {data['accel_y']:.3f}, {data['accel_z']:.3f}), "
                      f"زاویه=({data['gyro_x']:.3f}, {data['gyro_y']:.3f}, {data['gyro_z']:.3f})")
            import time as original_time
            original_time.sleep(0.1)
        
        print("✓ تست MPU6050 موفق")
        
    except Exception as e:
        print(f"✗ خطا در تست MPU6050: {e}")
        return False
    
    # تست کلاس CSVLogger
    print("\n--- تست کلاس CSVLogger ---")
    try:
        logger = CSVLogger("test_output.csv", buffer_size=3)
        
        # تولید و ذخیره داده‌های تست
        import time as original_time
        for i in range(5):
            test_data = {
                'sample_id': i + 1,
                'timestamp': int(original_time.time() * 1000),
                'accel_x': round(random.uniform(-2, 2), 4),
                'accel_y': round(random.uniform(-2, 2), 4),
                'accel_z': round(random.uniform(-2, 2), 4),
                'gyro_x': round(random.uniform(-250, 250), 4),
                'gyro_y': round(random.uniform(-250, 250), 4),
                'gyro_z': round(random.uniform(-250, 250), 4),
                'temperature': round(random.uniform(20, 30), 2)
            }
            logger.add_data(test_data)
            original_time.sleep(0.1)
        
        # ذخیره باقی‌مانده داده‌ها
        logger.flush_buffer()
        
        print("✓ تست CSVLogger موفق")
        
    except Exception as e:
        print(f"✗ خطا در تست CSVLogger: {e}")
        return False
    
    # بررسی فایل خروجی
    print("\n--- بررسی فایل خروجی ---")
    try:
        with open("test_output.csv", 'r') as f:
            content = f.read()
            lines = content.strip().split('\n')
            print(f"تعداد خطوط فایل: {len(lines)}")
            print("محتوای فایل:")
            for i, line in enumerate(lines[:6]):  # نمایش 6 خط اول
                print(f"  {i+1}: {line}")
            if len(lines) > 6:
                print(f"  ... و {len(lines) - 6} خط دیگر")
        
        print("✓ فایل CSV با موفقیت ایجاد شد")
        
    except Exception as e:
        print(f"✗ خطا در خواندن فایل: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("تمام تست‌ها با موفقیت انجام شدند! ✓")
    print("=" * 60)
    return True

if __name__ == "__main__":
    # اجرای تست
    success = test_mpu6050_logger()
    
    if success:
        print("\nسیستم آماده استفاده است!")
        print("برای اجرا روی ESP32:")
        print("1. فایل‌های mpu6050_csv_logger.py و config.py را روی ESP32 کپی کنید")
        print("2. اتصالات سخت‌افزاری را بررسی کنید")
        print("3. دستور main() را اجرا کنید")
    else:
        print("\nخطا در تست! لطفاً کد را بررسی کنید.")
