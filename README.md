# سیستم لاگ داده‌های MPU6050 برای ESP32

این پروژه یک سیستم بهینه‌شده برای خواندن داده‌های سنسور MPU6050 و ذخیره آن‌ها در فایل CSV با استفاده از ESP32 و MicroPython است.

## ویژگی‌ها

- ✅ خواندن داده‌های شتاب‌سنج (3 محور) و ژایروسکوپ (3 محور)
- ✅ ذخیره داده‌ها در فایل CSV با فاصله زمانی نیم ثانیه
- ✅ بهینه‌سازی مصرف حافظه با استفاده از بافرینگ
- ✅ مدیریت خطا و بازیابی خودکار
- ✅ تنظیمات قابل تغییر از طریق فایل config
- ✅ نمایش داده‌ها در کنسول
- ✅ شمارش نمونه‌ها و آمار

## فایل‌های پروژه

```
├── mpu6050_csv_logger.py    # کد اصلی سیستم
├── config.py                # فایل تنظیمات
├── test_mpu6050.py         # فایل تست
└── README.md               # راهنمای استفاده
```

## اتصالات سخت‌افزاری

### ESP32 به MPU6050

| ESP32 Pin | MPU6050 Pin | توضیحات |
|-----------|-------------|----------|
| 3.3V      | VCC         | تغذیه |
| GND       | GND         | زمین |
| GPIO 21   | SDA         | خط داده I2C |
| GPIO 22   | SCL         | خط کلاک I2C |

## نصب و راه‌اندازی

### 1. آماده‌سازی ESP32

```bash
# نصب MicroPython روی ESP32
esptool.py --chip esp32 --port /dev/ttyUSB0 erase_flash
esptool.py --chip esp32 --port /dev/ttyUSB0 write_flash -z 0x1000 esp32-*.bin
```

### 2. کپی فایل‌ها

فایل‌های زیر را روی ESP32 کپی کنید:
- `mpu6050_csv_logger.py`
- `config.py`

### 3. تنظیمات (اختیاری)

فایل `config.py` را ویرایش کنید:

```python
# تنظیمات پین‌های I2C
I2C_SCL_PIN = 22
I2C_SDA_PIN = 21

# فاصله زمانی خواندن (میلی‌ثانیه)
READ_INTERVAL_MS = 500  # نیم ثانیه

# اندازه بافر
BUFFER_SIZE = 5

# نام فایل خروجی
CSV_FILENAME = "mpu6050_data.csv"
```

## استفاده

### اجرای مستقیم

```python
# در REPL یا main.py
from mpu6050_csv_logger import main
main()
```

### اجرای تست

```python
# تست بدون سخت‌افزار
exec(open('test_mpu6050.py').read())
```

## فرمت فایل CSV

فایل خروجی شامل ستون‌های زیر است:

```csv
sample_id,timestamp,accel_x,accel_y,accel_z,gyro_x,gyro_y,gyro_z,temperature
1,12345,0.1234,-0.5678,0.9876,12.34,-56.78,98.76,25.4
2,12845,0.2345,-0.6789,1.0987,23.45,-67.89,109.87,25.5
...
```

### توضیح ستون‌ها

- `sample_id`: شماره نمونه
- `timestamp`: زمان خواندن (میلی‌ثانیه)
- `accel_x/y/z`: شتاب در محورهای X, Y, Z (g)
- `gyro_x/y/z`: سرعت زاویه‌ای در محورهای X, Y, Z (°/s)
- `temperature`: دمای سنسور (°C)

## بهینه‌سازی‌ها

### مدیریت حافظه
- استفاده از بافرینگ برای کاهش نوشتن مکرر فایل
- جمع‌آوری زباله خودکار
- محدود کردن دقت اعشار

### عملکرد
- خواندن غیرمسدود کننده
- مدیریت خطای I2C
- تاخیر بهینه CPU

### قابلیت اطمینان
- بازیابی خودکار از خطاها
- ذخیره داده‌های بافر در صورت قطع برنامه
- بررسی وجود فایل و نوشتن هدر

## عیب‌یابی

### خطاهای رایج

1. **خطای I2C**: بررسی اتصالات سیم‌کشی
2. **خطای فایل**: بررسی فضای ذخیره‌سازی
3. **خطای حافظه**: کاهش `BUFFER_SIZE`

### نمونه خروجی

```
==================================================
سیستم لاگ داده‌های MPU6050
==================================================
فایل خروجی: mpu6050_data.csv
فاصله زمانی خواندن: 500 میلی‌ثانیه
اندازه بافر: 5 رکورد
--------------------------------------------------
سنسور MPU6050 در آدرس 0x68 مقداردهی شد
هدر فایل mpu6050_data.csv نوشته شد
شروع جمع‌آوری داده‌ها...
برای توقف Ctrl+C را فشار دهید
--------------------------------------------------
نمونه 0001 | شتاب: (+0.123, -0.456, +0.987) g | زاویه: (+12.34, -56.78, +98.76) °/s
نمونه 0002 | شتاب: (+0.234, -0.567, +1.098) g | زاویه: (+23.45, -67.89, +109.87) °/s
...
```

## مشارکت

برای بهبود این پروژه:
1. مسائل را در Issues گزارش دهید
2. پیشنهادات خود را ارسال کنید
3. Pull Request ایجاد کنید

## مجوز

این پروژه تحت مجوز MIT منتشر شده است.

---

**نکته**: این کد برای استفاده آموزشی و تحقیقاتی طراحی شده است. برای استفاده تجاری، تست‌های بیشتری انجام دهید.
