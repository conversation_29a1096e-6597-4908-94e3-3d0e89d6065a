@echo off
chcp 65001 >nul
echo 🚀 اسکریپت آپلود ESP32 برای Windows
echo =======================================

REM بررسی وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python نصب نیست
    echo 💡 ابتدا Python را از python.org نصب کنید
    pause
    exit /b 1
)

REM بررسی وجود ampy
python -m pip show adafruit-ampy >nul 2>&1
if errorlevel 1 (
    echo 📦 در حال نصب ampy...
    python -m pip install adafruit-ampy
    if errorlevel 1 (
        echo ❌ خطا در نصب ampy
        pause
        exit /b 1
    )
    echo ✅ ampy نصب شد
)

REM درخواست پورت از کاربر
set /p PORT="🔌 پورت ESP32 را وارد کنید (مثل COM3): "
if "%PORT%"=="" (
    echo ❌ پورت وارد نشده
    pause
    exit /b 1
)

echo.
echo 📤 شروع آپلود فایل‌ها روی %PORT%...
echo.

REM آپلود فایل‌ها
echo [1/7] 📤 آپلود config.json...
ampy --port %PORT% put config.json
if errorlevel 1 goto :error

echo [2/7] 📤 آپلود config_loader.py...
ampy --port %PORT% put config_loader.py
if errorlevel 1 goto :error

echo [3/7] 📤 آپلود sensor_filters.py...
ampy --port %PORT% put sensor_filters.py
if errorlevel 1 goto :error

echo [4/7] 📤 آپلود mpu6050_enhanced.py...
ampy --port %PORT% put mpu6050_enhanced.py
if errorlevel 1 goto :error

echo [5/7] 📤 آپلود mpu6050_serial.py...
ampy --port %PORT% put mpu6050_serial.py
if errorlevel 1 goto :error

echo [6/7] 📤 آپلود dataset_creator.py...
ampy --port %PORT% put dataset_creator.py
if errorlevel 1 goto :error

echo [7/7] 📤 آپلود main.py...
ampy --port %PORT% put main.py
if errorlevel 1 goto :error

echo.
echo 🎉 همه فایل‌ها با موفقیت آپلود شدند!
echo 🚀 ESP32 آماده استفاده است
echo.
echo 📡 برای تست سریال:
echo    python serial_reader.py --port %PORT% --duration 30
echo.
pause
exit /b 0

:error
echo.
echo ❌ خطا در آپلود فایل
echo 🔧 راه‌حل‌های ممکن:
echo    - ESP32 را reset کنید
echo    - پورت صحیح را وارد کنید
echo    - کابل USB را بررسی کنید
echo    - دوباره تلاش کنید
echo.
pause
exit /b 1
