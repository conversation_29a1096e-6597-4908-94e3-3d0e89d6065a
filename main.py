"""
فایل اصلی ESP32 برای اجرای سنسور MPU6050
این فایل بعد از boot.py اجرا می‌شود
"""

import time

def auto_start():
    """شروع خودکار سیستم لاگ"""
    print("🚀 شروع سیستم خودکار...")
    print("⚠️  برای لغو Ctrl+C بزنید")

    # 5 ثانیه تاخیر برای امکان لغو
    for i in range(5, 0, -1):
        print(f"⏱️  شروع در {i} ثانیه...")
        time.sleep_ms(1000)

    try:
        # استفاده از نسخه سریال جدید
        from mpu6050_serial import main
        main()
    except KeyboardInterrupt:
        print("⏹️  سیستم لاگ متوقف شد")
    except ImportError as e:
        print(f"❌ خطا در وارد کردن ماژول: {e}")
        print("🔧 بررسی کنید که همه فایل‌ها آپلود شده باشند")
    except Exception as e:
        print(f"❌ خطا در اجرای سیستم: {e}")

if __name__ == "__main__":
    auto_start()
