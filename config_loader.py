"""
بارگذار تنظیمات از فایل JSON
این ماژول برای خواندن تنظیمات از config.json استفاده می‌شود
"""

import json
import os

class ConfigLoader:
    """کلاس بارگذاری تنظیمات"""
    
    def __init__(self, config_file="config.json"):
        """
        مقداردهی اولیه بارگذار تنظیمات
        
        Args:
            config_file (str): مسیر فایل تنظیمات JSON
        """
        self.config_file = config_file
        self.config = {}
        self.load_config()
    
    def load_config(self):
        """بارگذاری تنظیمات از فایل JSON"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                print(f"✅ تنظیمات از {self.config_file} بارگذاری شد")
            else:
                print(f"⚠️  فایل {self.config_file} یافت نشد - از تنظیمات پیش‌فرض استفاده می‌شود")
                self._load_default_config()
        except Exception as e:
            print(f"❌ خطا در بارگذاری تنظیمات: {e}")
            print("📝 از تنظیمات پیش‌فرض استفاده می‌شود")
            self._load_default_config()
    
    def _load_default_config(self):
        """بارگذاری تنظیمات پیش‌فرض"""
        self.config = {
            "hardware": {
                "i2c_scl_pin": 22,
                "i2c_sda_pin": 21,
                "i2c_frequency": 400000,
                "mpu6050_address": 104
            },
            "timing": {
                "read_interval_ms": 500,
                "sample_duration_ms": 3000
            },
            "csv": {
                "filename": "mpu6050_data.csv",
                "buffer_size": 5
            },
            "display": {
                "show_console_output": True,
                "decimal_places": 4
            },
            "memory": {
                "enable_garbage_collection": True,
                "gc_threshold": 10
            },
            "sensor": {
                "accel_scale": 16384.0,
                "gyro_scale": 131.0
            },
            "dataset": {
                "default_samples": 10,
                "default_rest_duration": 2,
                "samples_per_collection": 6
            }
        }
    
    def get(self, section, key, default=None):
        """
        دریافت مقدار تنظیمات
        
        Args:
            section (str): بخش تنظیمات
            key (str): کلید تنظیمات
            default: مقدار پیش‌فرض در صورت عدم وجود
            
        Returns:
            مقدار تنظیمات یا مقدار پیش‌فرض
        """
        try:
            return self.config[section][key]
        except KeyError:
            return default
    
    def get_hardware_config(self):
        """دریافت تنظیمات سخت‌افزاری"""
        return self.config.get("hardware", {})
    
    def get_timing_config(self):
        """دریافت تنظیمات زمان‌بندی"""
        return self.config.get("timing", {})
    
    def get_csv_config(self):
        """دریافت تنظیمات CSV"""
        return self.config.get("csv", {})
    
    def get_display_config(self):
        """دریافت تنظیمات نمایش"""
        return self.config.get("display", {})
    
    def get_memory_config(self):
        """دریافت تنظیمات حافظه"""
        return self.config.get("memory", {})
    
    def get_sensor_config(self):
        """دریافت تنظیمات سنسور"""
        return self.config.get("sensor", {})
    
    def get_dataset_config(self):
        """دریافت تنظیمات دیتاست"""
        return self.config.get("dataset", {})
    
    def save_config(self, config_file=None):
        """
        ذخیره تنظیمات در فایل JSON
        
        Args:
            config_file (str): مسیر فایل برای ذخیره (اختیاری)
        """
        file_path = config_file or self.config_file
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            print(f"✅ تنظیمات در {file_path} ذخیره شد")
            return True
        except Exception as e:
            print(f"❌ خطا در ذخیره تنظیمات: {e}")
            return False
    
    def update_config(self, section, key, value):
        """
        به‌روزرسانی تنظیمات
        
        Args:
            section (str): بخش تنظیمات
            key (str): کلید تنظیمات
            value: مقدار جدید
        """
        if section not in self.config:
            self.config[section] = {}
        self.config[section][key] = value
    
    def print_config(self):
        """نمایش تمام تنظیمات"""
        print("📋 تنظیمات فعلی:")
        print("=" * 50)
        for section, settings in self.config.items():
            if not section.startswith("_"):
                print(f"\n🔧 {section}:")
                for key, value in settings.items():
                    if not key.startswith("_"):
                        print(f"   {key}: {value}")
        print("=" * 50)

# ایجاد نمونه سراسری برای استفاده آسان
config_loader = ConfigLoader()

# متغیرهای سازگار با کد قدیمی
I2C_SCL_PIN = config_loader.get("hardware", "i2c_scl_pin", 22)
I2C_SDA_PIN = config_loader.get("hardware", "i2c_sda_pin", 21)
I2C_FREQUENCY = config_loader.get("hardware", "i2c_frequency", 400000)
MPU6050_ADDRESS = config_loader.get("hardware", "mpu6050_address", 104)

READ_INTERVAL_MS = config_loader.get("timing", "read_interval_ms", 500)
SAMPLE_DURATION_MS = config_loader.get("timing", "sample_duration_ms", 3000)

CSV_FILENAME = config_loader.get("csv", "filename", "mpu6050_data.csv")
BUFFER_SIZE = config_loader.get("csv", "buffer_size", 5)

SHOW_CONSOLE_OUTPUT = config_loader.get("display", "show_console_output", True)
DECIMAL_PLACES = config_loader.get("display", "decimal_places", 4)

ENABLE_GARBAGE_COLLECTION = config_loader.get("memory", "enable_garbage_collection", True)
GC_THRESHOLD = config_loader.get("memory", "gc_threshold", 10)

ACCEL_SCALE = config_loader.get("sensor", "accel_scale", 16384.0)
GYRO_SCALE = config_loader.get("sensor", "gyro_scale", 131.0)

DEFAULT_DATASET_SAMPLES = config_loader.get("dataset", "default_samples", 10)
DEFAULT_REST_DURATION = config_loader.get("dataset", "default_rest_duration", 2)
SAMPLES_PER_COLLECTION = config_loader.get("dataset", "samples_per_collection", 6)
