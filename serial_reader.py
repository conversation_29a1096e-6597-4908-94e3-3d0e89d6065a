#!/usr/bin/env python3
"""
خواننده سریال برای داده‌های MPU6050
خواندن داده‌ها از ESP32 و ذخیره در فایل CSV
"""

import serial
import json
import csv
import time
import threading
from datetime import datetime
import argparse

class MPU6050SerialReader:
    """کلاس خواندن داده‌های سریال MPU6050"""
    
    def __init__(self, port, baudrate=115200, timeout=1):
        """
        مقداردهی اولیه خواننده سریال
        
        Args:
            port (str): پورت سریال (مثل COM3 یا /dev/ttyUSB0)
            baudrate (int): سرعت ارتباط
            timeout (float): تایم‌اوت خواندن
        """
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.serial_conn = None
        self.is_running = False
        self.data_count = 0
        self.csv_writer = None
        self.csv_file = None
        
    def connect(self):
        """اتصال به پورت سریال"""
        try:
            self.serial_conn = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                timeout=self.timeout
            )
            print(f"✅ اتصال برقرار شد: {self.port} @ {self.baudrate}")
            time.sleep(2)  # انتظار برای آماده شدن ESP32
            return True
        except Exception as e:
            print(f"❌ خطا در اتصال سریال: {e}")
            return False
    
    def disconnect(self):
        """قطع اتصال سریال"""
        if self.serial_conn and self.serial_conn.is_open:
            self.serial_conn.close()
            print("🔌 اتصال سریال قطع شد")
    
    def setup_csv_file(self, filename=None):
        """راه‌اندازی فایل CSV"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"mpu6050_data_{timestamp}.csv"
        
        try:
            self.csv_file = open(filename, 'w', newline='', encoding='utf-8')
            self.csv_writer = csv.writer(self.csv_file)
            
            # نوشتن header
            header = ['sample_id', 'timestamp', 'accel_x', 'accel_y', 'accel_z', 
                     'gyro_x', 'gyro_y', 'gyro_z', 'angle_x', 'angle_y', 'angle_z', 'temperature']
            self.csv_writer.writerow(header)
            
            print(f"📁 فایل CSV ایجاد شد: {filename}")
            return filename
        except Exception as e:
            print(f"❌ خطا در ایجاد فایل CSV: {e}")
            return None
    
    def close_csv_file(self):
        """بستن فایل CSV"""
        if self.csv_file:
            self.csv_file.close()
            print(f"💾 فایل CSV بسته شد - {self.data_count} رکورد ذخیره شد")
    
    def parse_json_data(self, line):
        """پردازش داده JSON"""
        try:
            if line.startswith("DATA:"):
                json_str = line[5:]  # حذف "DATA:"
                data = json.loads(json_str)
                
                # تبدیل به فرمت CSV
                csv_row = [
                    data.get('id', 0),
                    data.get('time', 0),
                    data.get('ax', 0),
                    data.get('ay', 0),
                    data.get('az', 0),
                    data.get('gx', 0),
                    data.get('gy', 0),
                    data.get('gz', 0),
                    data.get('rx', 0),  # angle_x
                    data.get('ry', 0),  # angle_y
                    data.get('rz', 0),  # angle_z
                    data.get('temp', 0)
                ]
                
                return csv_row
        except Exception as e:
            print(f"⚠️  خطا در پردازش JSON: {e}")
        return None
    
    def parse_csv_data(self, line):
        """پردازش داده CSV"""
        try:
            if line.startswith("DATA:"):
                csv_str = line[5:]  # حذف "DATA:"
                values = csv_str.split(',')
                
                if len(values) >= 12:
                    return [float(v) if '.' in v else int(v) for v in values]
        except Exception as e:
            print(f"⚠️  خطا در پردازش CSV: {e}")
        return None
    
    def read_continuous(self, duration=None, max_samples=None, output_format="json"):
        """
        خواندن مداوم داده‌ها
        
        Args:
            duration (int): مدت زمان خواندن (ثانیه) - None برای نامحدود
            max_samples (int): حداکثر تعداد نمونه - None برای نامحدود
            output_format (str): فرمت داده ("json" یا "csv")
        """
        if not self.serial_conn or not self.serial_conn.is_open:
            print("❌ اتصال سریال برقرار نیست")
            return
        
        self.is_running = True
        start_time = time.time()
        
        print("🚀 شروع خواندن داده‌ها...")
        print("📊 فرمت:", "JSON" if output_format == "json" else "CSV")
        if duration:
            print(f"⏱️  مدت زمان: {duration} ثانیه")
        if max_samples:
            print(f"📈 حداکثر نمونه: {max_samples}")
        print("-" * 50)
        
        try:
            while self.is_running:
                # بررسی شرایط توقف
                if duration and (time.time() - start_time) > duration:
                    print(f"⏰ زمان تمام شد ({duration} ثانیه)")
                    break
                
                if max_samples and self.data_count >= max_samples:
                    print(f"📊 حداکثر نمونه رسید ({max_samples})")
                    break
                
                # خواندن خط
                try:
                    line = self.serial_conn.readline().decode('utf-8').strip()
                except UnicodeDecodeError:
                    continue
                
                if not line:
                    continue
                
                # پردازش خطوط مختلف
                if line.startswith("READY"):
                    print("✅ ESP32 آماده است")
                    continue
                elif line.startswith("STOPPED"):
                    print("⏹️  ESP32 متوقف شد")
                    break
                elif line.startswith("ERROR:"):
                    print(f"❌ خطا از ESP32: {line[6:]}")
                    continue
                elif line.startswith("DEBUG:"):
                    print(f"🔍 Debug: {line[6:]}")
                    continue
                elif line.startswith("HEADER:"):
                    print(f"📋 Header دریافت شد: {line[7:]}")
                    continue
                elif line.startswith("DATA:"):
                    # پردازش داده
                    if output_format == "json":
                        csv_row = self.parse_json_data(line)
                    else:
                        csv_row = self.parse_csv_data(line)
                    
                    if csv_row and self.csv_writer:
                        self.csv_writer.writerow(csv_row)
                        self.csv_file.flush()  # ذخیره فوری
                        self.data_count += 1
                        
                        # نمایش پیشرفت
                        if self.data_count % 10 == 0:
                            elapsed = time.time() - start_time
                            rate = self.data_count / elapsed if elapsed > 0 else 0
                            print(f"📈 {self.data_count} نمونه | {rate:.1f} Hz | "
                                  f"شتاب: ({csv_row[2]:.2f}, {csv_row[3]:.2f}, {csv_row[4]:.2f}) | "
                                  f"زاویه: ({csv_row[8]:.1f}°, {csv_row[9]:.1f}°, {csv_row[10]:.1f}°)")
                else:
                    # خطوط ناشناخته
                    if line and not line.startswith(('INFO:', 'WARNING:')):
                        print(f"📝 {line}")
        
        except KeyboardInterrupt:
            print("\n⚠️  متوقف شد توسط کاربر")
        except Exception as e:
            print(f"\n❌ خطا در خواندن: {e}")
        finally:
            self.is_running = False
            elapsed = time.time() - start_time
            print(f"\n📊 خلاصه:")
            print(f"   ⏱️  مدت زمان: {elapsed:.1f} ثانیه")
            print(f"   📈 تعداد نمونه: {self.data_count}")
            print(f"   🔄 نرخ نمونه‌برداری: {self.data_count/elapsed:.1f} Hz")

def main():
    """تابع اصلی"""
    parser = argparse.ArgumentParser(description='خواننده سریال MPU6050')
    parser.add_argument('--port', '-p', required=True, help='پورت سریال (مثل COM3 یا /dev/ttyUSB0)')
    parser.add_argument('--baudrate', '-b', type=int, default=115200, help='سرعت ارتباط (پیش‌فرض: 115200)')
    parser.add_argument('--duration', '-d', type=int, help='مدت زمان خواندن (ثانیه)')
    parser.add_argument('--samples', '-s', type=int, help='حداکثر تعداد نمونه')
    parser.add_argument('--output', '-o', help='نام فایل خروجی CSV')
    parser.add_argument('--format', '-f', choices=['json', 'csv'], default='json', help='فرمت داده (پیش‌فرض: json)')
    
    args = parser.parse_args()
    
    # ایجاد خواننده
    reader = MPU6050SerialReader(args.port, args.baudrate)
    
    try:
        # اتصال
        if not reader.connect():
            return
        
        # راه‌اندازی فایل CSV
        filename = reader.setup_csv_file(args.output)
        if not filename:
            return
        
        # شروع خواندن
        reader.read_continuous(
            duration=args.duration,
            max_samples=args.samples,
            output_format=args.format
        )
    
    finally:
        reader.close_csv_file()
        reader.disconnect()

if __name__ == "__main__":
    # مثال استفاده:
    # python serial_reader.py --port COM3 --duration 60 --samples 100
    # python serial_reader.py --port /dev/ttyUSB0 --format csv --output my_data.csv
    
    main()
