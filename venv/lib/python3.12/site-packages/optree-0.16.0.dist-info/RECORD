optree-0.16.0.dist-info/INSTALLER,sha256=5hhM4Q4mYTT9z6QB6PGpUAW81PGNFrYrdXMj4oM_6ak,2
optree-0.16.0.dist-info/METADATA,sha256=kVhYf_Gvvf5SKEiOzNsEu4g-9JUCoBHuN7u80dnwbzM,30873
optree-0.16.0.dist-info/RECORD,,
optree-0.16.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
optree-0.16.0.dist-info/WHEEL,sha256=aSgG0F4rGPZtV0iTEIfy6dtHq6g67Lze3uLfk0vWn88,151
optree-0.16.0.dist-info/licenses/LICENSE,sha256=_mSB_S6V5JPgawQiTVaP3Ajs2vt5QopxMMIplYrUey8,11370
optree-0.16.0.dist-info/top_level.txt,sha256=wxgWsTkpSlcz3dX9fMJb8ShwAlFdBUpvrlMRYE31_Po,7
optree/_C.cpython-312-x86_64-linux-gnu.so,sha256=E8HsYofWJMCIpmrSCHQZlr2GR5QwXKZ9AYkSyDBifdQ,823808
optree/_C.pyi,sha256=RM5jD9-VbczvtP_g9vnY310UT2xO6D__GNUbBmawCP4,6931
optree/__init__.py,sha256=2t_qhAhiNQESZV8rsYvOysIRyHFe2a5bhMqHYzFNWyU,6714
optree/accessor.py,sha256=WBt8lik5Rm1KlJ2DYuOHqtdCpsbVIe_6tS7mgq4yLco,1440
optree/accessors.py,sha256=S0ixH7hU8rorNZxGMozMhSAibmnU3Y7pVtjajYt7xBE,14452
optree/dataclasses.py,sha256=P9NHn5vpb4xw_uUv1xuo3KGMlw5Ios55KJAL689LWGA,20143
optree/functools.py,sha256=q8y9GEX31iQQBed-adOr7s_IDTEx7UzCqjL5YEt26aw,6416
optree/integration/__init__.py,sha256=dXLSuofEelG29vE4Id_na7VLZ_BNvz13mK4yM8TioGs,1495
optree/integration/jax.py,sha256=TAAVeIL8DxtEXT7XwGtMvHjmd80rdAK1GbvGJgkmLvA,1469
optree/integration/numpy.py,sha256=0ZZJ1qKlyAjfYTICv2yQtXTuKzycLe0P-8kgCBEG8TU,1481
optree/integration/torch.py,sha256=pMxUkRFWcDxiyLuCNqL8aNzDaJBGGDw3M7u30i5h-w4,1483
optree/integrations/__init__.py,sha256=QMC3xCNnpWColXkPIIY-TwtznpQF3G27NDyXLNMYxv8,1587
optree/integrations/jax.py,sha256=tNC5b5MOoEmCp8tYFLUrAI2ACyKysr6n_LZ9YP2MFdo,10824
optree/integrations/numpy.py,sha256=0MHih0sXTXV_5SpjD4L7g0VB-qbW-Ez3tlnmTPwaDiQ,7800
optree/integrations/torch.py,sha256=gnkLN7htGidUd2vb6Q4HdzPPZshVyWFaNMoN0cF0EaU,8120
optree/ops.py,sha256=TUbUWSTbn7zBDTOuenN1uGceNV-a1oCkeUFd3ffhE4Q,156682
optree/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
optree/pytree.py,sha256=tkMZ4GYtPhEN5LhBwTsf2Td4BESW6cgYw4cr4NVHTHw,14789
optree/registry.py,sha256=Sed8rMpO-sg1_itlBoAtO14ds2g2j469nl10VjxOpEM,30200
optree/treespec.py,sha256=CWpF1E2LCe-4z14HpHcrOGkwG-kokmvROtJmMxbBRys,1923
optree/typing.py,sha256=Dh1GHH0rLuxStkX_QqtPTC6CVJZqaS08EIaLNzVHlJc,19588
optree/utils.py,sha256=7trPsYmQqEPMsErFC5_sIo5VkvHWT57C_tHq2-rZ44g,3498
optree/version.py,sha256=2fMP3fXyxmvbgfcafkr8Y-Fm1jl2xwymmI3C_rG8oFA,2007
