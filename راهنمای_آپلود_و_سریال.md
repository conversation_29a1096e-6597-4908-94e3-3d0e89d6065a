# 📤 راهنمای آپلود فایل‌ها و خواندن سریال

## 📋 فایل‌های مورد نیاز برای آپلود روی ESP32

### 🔴 فایل‌های ضروری:
```
📁 فایل‌های اصلی (حتماً آپلود کنید):
├── config.json              # تنظیمات سیستم
├── config_loader.py         # بارگذار تنظیمات  
├── sensor_filters.py        # فیلترهای نویز
├── mpu6050_enhanced.py      # سنسور پیشرفته
├── mpu6050_serial.py        # نسخه سریال
└── dataset_creator.py       # ایجادکننده دیتاست

📁 فایل‌های اختیاری:
├── boot.py                  # راه‌اندازی خودکار
└── main.py                  # شروع خودکار
```

### 📤 نحوه آپلود:

#### روش 1: Thonny IDE
1. باز کردن Thonny
2. اتصال به ESP32
3. کلیک راست روی فایل → "Upload to /"
4. تکرار برای همه فایل‌ها

#### روش 2: ampy
```bash
# نصب ampy
pip install adafruit-ampy

# آپلود فایل‌ها
ampy --port COM3 put config.json
ampy --port COM3 put config_loader.py
ampy --port COM3 put sensor_filters.py
ampy --port COM3 put mpu6050_enhanced.py
ampy --port COM3 put mpu6050_serial.py
ampy --port COM3 put dataset_creator.py
```

#### روش 3: rshell
```bash
# نصب rshell
pip install rshell

# اتصال و آپلود
rshell --port COM3
cp config.json /pyboard/
cp config_loader.py /pyboard/
cp sensor_filters.py /pyboard/
cp mpu6050_enhanced.py /pyboard/
cp mpu6050_serial.py /pyboard/
cp dataset_creator.py /pyboard/
```

## 🔌 راه‌اندازی سریال

### 1. فایل main.py برای ESP32:
```python
# main.py - آپلود روی ESP32
from mpu6050_serial import main

if __name__ == "__main__":
    main()
```

### 2. خواندن از طریق Python:

#### نصب کتابخانه سریال:
```bash
pip install pyserial
```

#### استفاده از serial_reader.py:

**Windows:**
```bash
# خواندن 60 ثانیه از COM3
python serial_reader.py --port COM3 --duration 60

# خواندن 100 نمونه
python serial_reader.py --port COM3 --samples 100

# ذخیره در فایل مشخص
python serial_reader.py --port COM3 --output my_data.csv --duration 30
```

**Linux/Mac:**
```bash
# یافتن پورت
ls /dev/tty*

# خواندن داده‌ها
python serial_reader.py --port /dev/ttyUSB0 --duration 60
python serial_reader.py --port /dev/ttyACM0 --samples 200
```

## 📊 فرمت‌های خروجی

### 1. فرمت JSON (پیش‌فرض):
```
DATA:{"id":1,"time":12345,"ax":0.12,"ay":-0.34,"az":0.98,"gx":1.2,"gy":-2.1,"gz":0.5,"rx":15.2,"ry":-8.1,"rz":180.3,"temp":25.4}
```

### 2. فرمت CSV:
```
DATA:1,12345,0.12,-0.34,0.98,1.2,-2.1,0.5,15.2,-8.1,180.3,25.4
```

### 3. فایل CSV نهایی:
```csv
sample_id,timestamp,accel_x,accel_y,accel_z,gyro_x,gyro_y,gyro_z,angle_x,angle_y,angle_z,temperature
1,12345,0.12,-0.34,0.98,1.2,-2.1,0.5,15.2,-8.1,180.3,25.4
2,12845,0.15,-0.31,0.95,1.1,-2.3,0.7,15.8,-7.9,181.1,25.3
```

## 🔧 تنظیمات config.json

```json
{
  "serial": {
    "output_format": "json",     // "json" یا "csv"
    "include_debug": false,      // true برای debug
    "baudrate": 115200
  },
  "timing": {
    "read_interval_ms": 500      // فاصله خواندن (میلی‌ثانیه)
  },
  "sensor": {
    "enable_filtering": true,    // فعال/غیرفعال فیلترها
    "filter_alpha": 0.8,         // ضریب فیلتر (0-1)
    "noise_threshold": 0.01      // آستانه نویز
  }
}
```

## 🚀 مثال کامل استفاده

### 1. آپلود فایل‌ها روی ESP32
### 2. ایجاد main.py:
```python
from mpu6050_serial import main
main()
```

### 3. اجرای خواننده سریال:
```bash
# خواندن 2 دقیقه و ذخیره
python serial_reader.py --port COM3 --duration 120 --output experiment_1.csv

# خواندن 500 نمونه
python serial_reader.py --port COM3 --samples 500 --output dataset_500.csv
```

### 4. خروجی مثال:
```
✅ اتصال برقرار شد: COM3 @ 115200
📁 فایل CSV ایجاد شد: experiment_1.csv
🚀 شروع خواندن داده‌ها...
📊 فرمت: JSON
⏱️  مدت زمان: 120 ثانیه
--------------------------------------------------
✅ ESP32 آماده است
📈 10 نمونه | 2.0 Hz | شتاب: (0.12, -0.34, 0.98) | زاویه: (15.2°, -8.1°, 180.3°)
📈 20 نمونه | 2.0 Hz | شتاب: (0.15, -0.31, 0.95) | زاویه: (15.8°, -7.9°, 181.1°)
...
⏰ زمان تمام شد (120 ثانیه)

📊 خلاصه:
   ⏱️  مدت زمان: 120.1 ثانیه
   📈 تعداد نمونه: 240
   🔄 نرخ نمونه‌برداری: 2.0 Hz
💾 فایل CSV بسته شد - 240 رکورد ذخیره شد
🔌 اتصال سریال قطع شد
```

## 🛠️ عیب‌یابی

### مشکلات رایج:

1. **پورت پیدا نمی‌شود**:
   ```bash
   # Windows
   python -m serial.tools.list_ports
   
   # Linux
   ls /dev/tty*
   ```

2. **خطای Permission (Linux)**:
   ```bash
   sudo usermod -a -G dialout $USER
   # سپس logout/login
   ```

3. **ESP32 پاسخ نمی‌دهد**:
   - بررسی اتصالات سنسور
   - Reset کردن ESP32
   - بررسی فایل‌های آپلود شده

4. **داده‌های نامعقول**:
   - بررسی تنظیمات فیلتر
   - کالیبراسیون سنسور

### کدهای تشخیص:

```python
# تست اتصال سریال
import serial
ser = serial.Serial('COM3', 115200, timeout=1)
print(ser.readline().decode())

# تست ESP32
# در REPL ESP32:
from mpu6050_serial import MPU6050Serial
mpu = MPU6050Serial()
data = mpu.read_sensor_data()
print(data)
```

## 📈 نکات بهینه‌سازی

1. **سرعت بالا**: baudrate = 921600 (در صورت نیاز)
2. **فیلتر کمتر**: filter_alpha = 0.9
3. **فاصله کمتر**: read_interval_ms = 100 (10 Hz)
4. **بدون debug**: include_debug = false

---
**نکته مهم**: حتماً همه فایل‌های ضروری را آپلود کنید، وگرنه سیستم کار نخواهد کرد!
