<!DOCTYPE HTML>
<html>

<head>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/paho-mqtt/1.0.1/mqttws31.min.js"></script>
    <script src="https://canvasjs.com/assets/script/canvasjs.min.js"></script>
    <script>
        window.onload = function () {

            ////////////////////// Mqtt
            var options = {
                host: "broker.mqttdashboard.com",
                port: 8000,
                clientID: "web" + new Date().getTime(),
                connectOpts: {
                    keepAliveInterval: 30,
                    timeout: 10,
                    cleanSession: true,
                    onSuccess: onConnect,
                    onFailure: onFailure,
                    useSSL: false,
                }
            }
            var client = new Paho.MQTT.Client(options.host, options.port, options.clientID);
            client.onMessageArrived = onMessage;
            client.onconnectionlost = onDisconnect;

            function onConnect() {
                document.getElementById('status').innerHTML = "mqtt connected";
                client.subscribe("micropython/test/mpu6050", {qos:0});
            }

            function onFailure() { 
                document.getElementById('status').innerHTML = "failed to connect";
                reconnect();
            }
            client.connect(options.connectOpts);

            function onDisconnect(reason) {
                console.log("disconnected - " + reason);
                document.getElementById('status').innerHTML = "Disconnected";
                reconnect();
            }

            function reconnect() {
                setTimeout(function () {
                    document.getElementById('status').innerHTML = "reconnecting";
                    client.connect(options.connectOpts);
                }, 3000);
            }


            function onMessage(message) {
                var m = JSON.parse(message.payloadString);
                console.log(m);
                addData("accChartId", m);
            }

            ////////////////////// Chart
            var listAccX = [];
            var listAccY = [];
            var listAccZ = [];
            var listGyX = [];
            var listGyY = [];
            var listGyZ = [];

            var chartAcc = new CanvasJS.Chart("accChartId", {
                title: {
                    text: "Accerlerometer XYZ"
                },
                axisY: {
                    includeZero: false
                },
                data: [
                    {
                        name: "Acc X",
                        type: "line",
                        dataPoints: listAccX
                    },
                    {
                        name: "Acc Y",
                        type: "line",
                        dataPoints: listAccY
                    },
                    {
                        name: "Acc Z",
                        type: "line",
                        dataPoints: listAccZ
                    },
                ]
            });

            var chartGy = new CanvasJS.Chart("gyroChartId", {
                title: {
                    text: "Gyro XYZ"
                },
                axisY: {
                    includeZero: false
                },
                data: [
                    {
                        name: "Gyro X",
                        type: "line",
                        dataPoints: listGyX
                    },
                    {
                        name: "Gyro Y",
                        type: "line",
                        dataPoints: listGyY
                    },
                    {
                        name: "Gyro Z",
                        type: "line",
                        dataPoints: listGyZ
                    },
                ]
            });

            var dataLength = 250; // number of dataPoints visible at any point
            var addData = function (chartId, data) {
                //console.log(data);
                listAccX.push({ x: data.idx, y: data.accX });
                listAccY.push({ x: data.idx, y: data.accY });
                listAccZ.push({ x: data.idx, y: data.accZ });
                
                listGyX.push({ x: data.idx, y: data.gyX });
                listGyY.push({ x: data.idx, y: data.gyY });
                listGyZ.push({ x: data.idx, y: data.gyZ });


                if (listAccX.length > dataLength) {
                    for (let index = 0; index < listAccX.length - dataLength; index++) {
                        listAccX.shift();
                        listAccY.shift();
                        listAccZ.shift();

                        listGyX.shift();
                        listGyY.shift();
                        listGyZ.shift();
                    }
                }
                chartAcc.render();
                chartGy.render();
            };

        }
    </script>
</head>

<body>
    <div id=status>Disconnected</div>
    <div>
        <div id="accChartId" style="height: 370px; width:100%;"></div>
    </div>
    <div>
        <div id="gyroChartId" style="height: 370px; width:100%;"></div>
    </div>
</body>

</html>