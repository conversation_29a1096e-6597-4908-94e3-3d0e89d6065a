

from machine import I2C, Pin
import mpu6050
from umqtt.simple import MQTT<PERSON>lient
import time
import json
import urandom

time.sleep_ms(1000)

i2c = I2C(scl=Pin(22), sda=Pin(21))
mpu = mpu6050.accel(i2c)
c = MQTTClient(str(urandom.getrandbits(32)), "broker.mqttdashboard.com")
c.connect()


# --- Configuration ---
# Time between samples in milliseconds. 500ms = 0.5s
SAMPLE_INTERVAL_MS = 500
# Duration of each data sample window in seconds.
SAMPLE_WINDOW_S = 3
# Calculate how many readings are in a single window.
# (3s * 1000ms/s) / 500ms/reading = 6 readings
SAMPLES_PER_WINDOW = int((SAMPLE_WINDOW_S * 1000) / SAMPLE_INTERVAL_MS)
# MQTT topic for publishing the data.
MQTT_TOPIC = b"micropython/smart_helmet/data"

print("Starting data collection...")

# --- Main Loop ---
# This loop runs forever, collecting data in 3-second windows.
while True:
    # A list to store the readings for one sample window.
    sample_window_data = []
    print(f"--- Collecting a new {SAMPLE_WINDOW_S}-second sample ---")

    # Loop to collect all readings for one window.
    for i in range(SAMPLES_PER_WINDOW):
        try:
            # Get sensor values from the MPU6050.
            values = mpu.get_values()
            sample_window_data.append(values)
            print(f"Reading {i + 1}/{SAMPLES_PER_WINDOW}: {values}")

        except Exception as e:
            # Handle cases where the sensor might fail to return data.
            print(f"Error reading sensor: {e}")
            sample_window_data.append({'error': str(e)})

        # Wait for the specified interval before the next reading.
        time.sleep_ms(SAMPLE_INTERVAL_MS)

    # --- Data Processing and Publishing ---
    # At this point, a full 3-second window of data has been collected.
    print(f"--- Sample collection finished. Collected {len(sample_window_data)} readings. ---")

    # The collected data can now be used to build a dataset.
    # For now, we will publish the entire window to the MQTT broker.
    try:
        # Publish the entire list of readings as a single JSON string.
        c.publish(MQTT_TOPIC, json.dumps(sample_window_data))
        print(f"Successfully published sample window to topic: {MQTT_TOPIC}")
    except Exception as e:
        # Handle potential network or MQTT broker issues.
        print(f"Failed to publish to MQTT: {e}")

    print("\n") # Add a newline for cleaner output separation.
