../../../bin/f2py,sha256=HLorNktFRTXHPlxtZ8FWetJKjuozjI0ZR1wbB2EDTsI,333
../../../bin/numpy-config,sha256=_G6p6OlZJeZCcffaLzDwzGLmXQbg38csO4Txm68SgdI,333
numpy-2.1.3.dist-info/INSTALLER,sha256=5hhM4Q4mYTT9z6QB6PGpUAW81PGNFrYrdXMj4oM_6ak,2
numpy-2.1.3.dist-info/LICENSE.txt,sha256=wAK9Jt59x6pGQlCg3gY9WP5Vl0RS5DieXCHDUKggvwY,47755
numpy-2.1.3.dist-info/METADATA,sha256=fAd0HaSdw683in0itVSnw4FaB4Tm5K3M9rcVouzmRN4,62026
numpy-2.1.3.dist-info/RECORD,,
numpy-2.1.3.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy-2.1.3.dist-info/WHEEL,sha256=3qIDcXCk577AXiK3pDifO-gE9U_MYWYGgtD78gLa2_U,137
numpy-2.1.3.dist-info/entry_points.txt,sha256=4mXDNhJDQ9GHqMBeRJ8B3PlixTFmkXGqU3RVuac20q0,172
numpy.libs/libgfortran-040039e1-0352e75f.so.5.0.0,sha256=xgkASOzMdjUiwS7wFvgdprYnyzoET1XPBHmoOcQcCYA,2833617
numpy.libs/libquadmath-96973f99-934c22de.so.0.0.0,sha256=btUTf0Enga14Y0OftUNhP2ILQ8MrYykqACkkYWL1u8Y,250985
numpy.libs/libscipy_openblas64_-ff651d7f.so,sha256=GJqD7zg8JOy80oVVqeJJ_-sNPrfSCTc7SuUn2RBOQ9A,22419249
numpy/__config__.py,sha256=CoEzpY_vMYhK_T6P5yHbRyFxVnzw3SaYtctAKLJ2GX4,5128
numpy/__init__.cython-30.pxd,sha256=2q-p82Z36yscZNHW1FDTo7DELx6wv06_kaX-HtvnApM,45784
numpy/__init__.pxd,sha256=uEqFZ3kgTA7uKP-67XAj4410tQ6ZfHxuy5mnWTx_2zI,42418
numpy/__init__.py,sha256=OcQtsCdUj5WOCW6Lq-P6Dj53PSSqOetjY_wOOrvsNLE,22007
numpy/__init__.pyi,sha256=2sZwIRwaCQeU1UFPI8mBrMKeYkxj3HNSdR2pIt-eyz0,150455
numpy/_array_api_info.py,sha256=qiHJDVG58rAk1iTlXsFrnhZ7Y-ghPUkyBpJiMvPK2jg,10381
numpy/_array_api_info.pyi,sha256=4BzgW4K9n_ZNxeY-Htyr1QohEPvAENeoD6WUrUN0_pM,5025
numpy/_configtool.py,sha256=asiPfz_TX2Dp0msoNjG43pZKRYgNYusSIg2ieczK8as,1007
numpy/_core/__init__.py,sha256=H95-zST0CH6pnnObjXUXXiPgtub9M35IBGaYE-q4wrU,5612
numpy/_core/__init__.pyi,sha256=Mj2I4BtqBVNUZVs5o1T58Z7wSaWjfhX0nCl-a0ULjgA,86
numpy/_core/_add_newdocs.py,sha256=SxbBrYFDlenUqTgO9QNHV9bhWwISlP0BvfWnr0hhcf0,211380
numpy/_core/_add_newdocs_scalars.py,sha256=99L7x4iLLYNeVQmiqik-NgEFzwNxD8gnKRkaynze-nY,12613
numpy/_core/_asarray.py,sha256=qHfjtg7BDT29rG_AJsqRhyBV16UF3WL7xgDVPuYHb8w,3910
numpy/_core/_asarray.pyi,sha256=UgVEqBCv5MbJkXSYsVoG6a_4ARTIoKHeeouyG0LPMH8,1041
numpy/_core/_dtype.py,sha256=4Pz6KJQJRywlsMhdH8NbIugziDyQi1ekv2ZMw7zomzo,10734
numpy/_core/_dtype_ctypes.py,sha256=dcZHQ46qjV0n7l934WIYw7kv-1HoHxelu50oIIX7GWU,3718
numpy/_core/_exceptions.py,sha256=dZWKqfdLRvJvbAEG_fof_8ikEKxjakADMty1kLC_l_M,5379
numpy/_core/_internal.py,sha256=B8t6mxvaDouxE-COR010v4_PUHNzOF8mHgFatRPlJWk,29164
numpy/_core/_internal.pyi,sha256=06EhTNYJ7HUtuV-oFz14OijSOCkT8f71-qBc7GOrCGk,1022
numpy/_core/_machar.py,sha256=ZGDDdOxsfa2JBZdWcRpUAFHZPAC2nAQnjqceiY7bWjg,11566
numpy/_core/_methods.py,sha256=EemMgHPaeXhYegA6kGOVpka6psheHyq8NbII4nGprxk,9655
numpy/_core/_multiarray_tests.cpython-312-x86_64-linux-gnu.so,sha256=DS0m0ffYzwPcFndLy3oU9abDBgDEDUfJDBkyIElD5Tg,178888
numpy/_core/_multiarray_umath.cpython-312-x86_64-linux-gnu.so,sha256=qU0_5b4HaYfE-nDQY_BjZJM7fTyW7bDZL_83UFUdMP4,10490129
numpy/_core/_operand_flag_tests.cpython-312-x86_64-linux-gnu.so,sha256=4TpRx3XIlThfYr2EyAfFB7zRxId8iOK_HYpVEwsasX8,16984
numpy/_core/_rational_tests.cpython-312-x86_64-linux-gnu.so,sha256=47Fmzv1V5uHVoclDxkyOYr2C3teLr3K6Uu3gdY3jY1g,59832
numpy/_core/_simd.cpython-312-x86_64-linux-gnu.so,sha256=mIi7KpJ0Mt0JD0JrBDZS3Wh2kttsOmrzSG0879UO4u8,3042312
numpy/_core/_string_helpers.py,sha256=gu3x0dEnRnh3mnOkviX17r8rCmagVgYHfxILt9Q9irA,2837
numpy/_core/_struct_ufunc_tests.cpython-312-x86_64-linux-gnu.so,sha256=A8z-Q7TayOW61icUKyxvrWlopXsjY4UK8gZxcxgdsWw,17120
numpy/_core/_type_aliases.py,sha256=3MfPJXIhE0-Gxsmw3HZTlLgulAY9lowvyrjjzzxY77Y,3493
numpy/_core/_type_aliases.pyi,sha256=pQ0FXzLol6L7XATTBfrbpNwEay-P0cl2DZVYqM43o7E,70
numpy/_core/_ufunc_config.py,sha256=nIrlf66xV1h9WMM4mNs9k268ljZyy-bGTuo0_OBSsMc,15577
numpy/_core/_ufunc_config.pyi,sha256=-615enOVQMBhVx7Pln7DY_s4H6JjSgSnBy89YkpvuLg,1066
numpy/_core/_umath_tests.cpython-312-x86_64-linux-gnu.so,sha256=dIaC4wrKI9gy4FcSNeu8ztU6SoMWPLEiMUwbbXRqrmU,50512
numpy/_core/arrayprint.py,sha256=4pgm3ik6GXLsl3e8hYbvjVp3wMRLQ9g7xz4E1P_CJ5I,64286
numpy/_core/arrayprint.pyi,sha256=X057JuO_ZrmXRkDnbIHW_Yefnu76wQukCs5YoErB3SY,4298
numpy/_core/cversions.py,sha256=H_iNIpx9-hY1cQNxqjT2d_5SXZhJbMo_caq4_q6LB7I,347
numpy/_core/defchararray.py,sha256=WL-Q0swlMo0Lbl3TRNaKyozEijhNdZq568HtEe1KRk8,37508
numpy/_core/defchararray.pyi,sha256=RpuCpG59ykbW_fze6TZDvmC5VhN43-3hac6DnTLmDLg,19812
numpy/_core/einsumfunc.py,sha256=066W9VApLUi5TL22tRYT3729fdY0KqAdudtiY18d5nc,52921
numpy/_core/einsumfunc.pyi,sha256=Ai7745UQf8-oZgrDod9z2NMj8BrDNJYzJWTltSDZd5Q,4821
numpy/_core/fromnumeric.py,sha256=DA7ghaA06vZ-rlJgsb8avGwoJe3eaJdKPGQuHwdIG40,144225
numpy/_core/fromnumeric.pyi,sha256=U_dlNA1xuGOgFPkccPgVY-L8JC-wjvtVOElLULKIhC0,28362
numpy/_core/function_base.py,sha256=FHE1Wt8YjmXHEtRwgLe-Xyy-CToe2FLIbLEcmoCpvS8,20026
numpy/_core/function_base.pyi,sha256=lMuu0qyK6IHl17CSdDxK7o6TTDYTvfNfd14GLe2Zv8Y,5021
numpy/_core/getlimits.py,sha256=FS5i6vxu8Snq3173Q4VhVcGY_ygZYIam6m8gpwxKawQ,25985
numpy/_core/getlimits.pyi,sha256=qeIXUEtognTHr_T-tv-VcZI7n8Z2VzAyIpIgKXzsLkc,82
numpy/_core/include/numpy/__multiarray_api.c,sha256=u7HxPIx7xdxAPTE0gristUOO0-1L-_fl0IeKqR4voxI,12669
numpy/_core/include/numpy/__multiarray_api.h,sha256=akdAXdNQvHxPFPbdeobhoGzyLUkoVdwzKDjzdbtk5zQ,61383
numpy/_core/include/numpy/__ufunc_api.c,sha256=Fg7WlH4Ow6jETKRArVL_QF11ABKYz1VpOve56_U3E0w,1755
numpy/_core/include/numpy/__ufunc_api.h,sha256=tayZuDCeuqm3ggFvWxJuoARz5obz6Saas9L7JcKO_eQ,13166
numpy/_core/include/numpy/_neighborhood_iterator_imp.h,sha256=s-Hw_l5WRwKtYvsiIghF0bg-mA_CgWnzFFOYVFJ-q4k,1857
numpy/_core/include/numpy/_numpyconfig.h,sha256=brqqDI4gwfGEFHMIWi0oNA0n_qnBBUWFVJtgfcdpSA0,926
numpy/_core/include/numpy/_public_dtype_api_table.h,sha256=n6_Kb98SyvsR_X7stiNA6VuGp_c5W1e4fMVcJdO0wis,4574
numpy/_core/include/numpy/arrayobject.h,sha256=mU5vpcQ95PH1j3bp8KYhJOFHB-GxwRjSUsR7nxlTSRk,204
numpy/_core/include/numpy/arrayscalars.h,sha256=LlyrZIa_5td11BfqfMCv1hYbiG6__zxxGv1MRj8uIVo,4243
numpy/_core/include/numpy/dtype_api.h,sha256=nCsBY26NtXTFaglc-2Jekmsuws6rmaLi9hhYZjN_pw8,19192
numpy/_core/include/numpy/halffloat.h,sha256=TRZfXgipa-dFppX2uNgkrjrPli-1BfJtadWjAembJ4s,1959
numpy/_core/include/numpy/ndarrayobject.h,sha256=MnykWmchyS05ler_ZyhFIr_0j6c0IcndEi3X3n0ZWDk,12057
numpy/_core/include/numpy/ndarraytypes.h,sha256=Ll-jCTQ3zMkZQzLCXNNRtGRxSq-TQRw6mUxos6MTzlw,64951
numpy/_core/include/numpy/npy_1_7_deprecated_api.h,sha256=90kGcNaBPgT5FJArB_MPgW24_Mpl5RcfUR3Y0rRB5Bw,3746
numpy/_core/include/numpy/npy_2_compat.h,sha256=wdjB7_-AtW3op67Xbj3EVH6apSF7cRG6h3c5hBz-YMs,8546
numpy/_core/include/numpy/npy_2_complexcompat.h,sha256=eE9dV_Iq3jEfGGJFH_pQjJnvC6eQ12WgOB7cZMmHByE,857
numpy/_core/include/numpy/npy_3kcompat.h,sha256=grN6W1n7benj3F2pSAOpl_s6vn1Y50QfAP-DaleD7cA,9648
numpy/_core/include/numpy/npy_common.h,sha256=wbV1Z6m3w1h4qVcOxfF38s3H13UfFHEuBGRfDhTeUKE,36551
numpy/_core/include/numpy/npy_cpu.h,sha256=pcVRtj-Y6120C5kWB1VAiAjZoxkTPDEg0gGm5IAt3jM,4629
numpy/_core/include/numpy/npy_endian.h,sha256=we7X9fPeWzNpo_YTh09MPGDwdE0Rw_WDM4c9y4nBj5I,2786
numpy/_core/include/numpy/npy_math.h,sha256=YoJBuiXRXnq0_1tZ-EGvTcVP3DWUV_QZc3JRJs-Kx-k,18890
numpy/_core/include/numpy/npy_no_deprecated_api.h,sha256=0yZrJcQEJ6MCHJInQk5TP9_qZ4t7EfBuoLOJ34IlJd4,678
numpy/_core/include/numpy/npy_os.h,sha256=hlQsg_7-RkvS3s8OM8KXy99xxyJbCm-W1AYVcdnO1cw,1256
numpy/_core/include/numpy/numpyconfig.h,sha256=OvRlre4eb9KBWt6gAE5cQ4K-P2uRmIKU1rAKxWFygmA,7161
numpy/_core/include/numpy/random/LICENSE.txt,sha256=-8U59H0M-DvGE3gID7hz1cFGMBJsrL_nVANcOSbapew,1018
numpy/_core/include/numpy/random/bitgen.h,sha256=49AwKOR552r-NkhuSOF1usb_URiMSRMvD22JF5pKIng,488
numpy/_core/include/numpy/random/distributions.h,sha256=W5tOyETd0m1W0GdaZ5dJP8fKlBtsTpG23V2Zlmrlqpg,9861
numpy/_core/include/numpy/random/libdivide.h,sha256=ew9MNhPQd1LsCZiWiFmj9IZ7yOnA3HKOXffDeR9X1jw,80138
numpy/_core/include/numpy/ufuncobject.h,sha256=cgEIXDsLhdY55HxNK9i4BAVrq4Q2LQ1WRZ8PCvsY-hQ,11911
numpy/_core/include/numpy/utils.h,sha256=wMNomSH3Dfj0q78PrjLVtFtN-FPo7UJ4o0ifCUO-6Es,1185
numpy/_core/lib/libnpymath.a,sha256=Rg3gCXTxpny2Hh-jZFKh6KDYquzjcJklumEnLHhQXQ0,118712
numpy/_core/lib/npy-pkg-config/mlib.ini,sha256=_LsWV1eStNqwhdiYPa2538GL46dnfVwT4MrI1zbsoFw,147
numpy/_core/lib/npy-pkg-config/npymath.ini,sha256=0iMzarBfkkZ_EXO95_kz-SHZRcNIEwIeOjE_esVBkRQ,361
numpy/_core/lib/pkgconfig/numpy.pc,sha256=jflW6SYfGuV3RcNbLPxoCSLGsT5MetvH4QvOnDXY7HQ,191
numpy/_core/memmap.py,sha256=5CDRv-7BkIla30Hox-VCM411JVSV9axG5fRc-3S94IM,12211
numpy/_core/memmap.pyi,sha256=sxIQ7T5hPLG-RBNndAc8JPvrsKEX1amBSH2HGg48Obo,55
numpy/_core/multiarray.py,sha256=YPRRXD7gK8_ZMNFFwQpG7o7uX3d40uyJjrhKv3rDsnc,57646
numpy/_core/multiarray.pyi,sha256=SgwPTH-HjURp_Q5dautihGPiZaUB4F9_Ee2-mj-jGbk,26510
numpy/_core/numeric.py,sha256=cdV8daLwmQY3MnqjEhvt959caY6-rNzSV-uAnB5RU0Y,81825
numpy/_core/numeric.pyi,sha256=-Vg0Au46q_CRcFhQP6aKnXdrtTIu7Z9cg5Cc_qFPHXU,15313
numpy/_core/numerictypes.py,sha256=7JwlBGlxoiIMZ2td4JD8bo7jIjL7sc1LgR5WFBWEc5w,16113
numpy/_core/numerictypes.pyi,sha256=4BLrwt9_YHKJydksp09ZFVI3IpFPkDNeTTPRtlpmt-4,1680
numpy/_core/overrides.py,sha256=uq6llUwm2-tnF4FNrEHoQ09aoqbo_Xf-WNj4Z1coPK0,7094
numpy/_core/printoptions.py,sha256=FUY--hG0-oobvtHOY64D50Bs_-JFmf-Nza7C9IXORFY,1063
numpy/_core/records.py,sha256=xvHJmb2JuJS8W7au5BvM--CI1jnANg-dxJbUzHSULok,36867
numpy/_core/records.pyi,sha256=M1XTqfVc_fH5m_ya8kUBYLChgXNfi_tI9fsoa1TYDA0,8774
numpy/_core/shape_base.py,sha256=JGQ0m-v3z-vTJ6Nw4cETtIh7q3ZbiTn32Dc7wIwm8vA,32429
numpy/_core/shape_base.pyi,sha256=grYlKtPWTZnhtysCSiXcD-XtUB8zC6jRHshKZwtB3VE,3031
numpy/_core/strings.py,sha256=u5-8OqFaRSYNDPMQWC4AEHPvpFDWSuWSZlz7DR-J-iE,44229
numpy/_core/strings.pyi,sha256=qv0ms8Nj9nrTQ5d6qzfEaU-g490jX3TKXvxJZXzTwNw,7520
numpy/_core/tests/_locales.py,sha256=S4x5soqF0oxpBYOE8J9Iky72O9J25IiZ8349m93pWC4,2206
numpy/_core/tests/_natype.py,sha256=9N-pE9LuQKrqT7ef-P9mtXpWls3YAsZ8JR-3cR7TRjs,6259
numpy/_core/tests/data/astype_copy.pkl,sha256=lWSzCcvzRB_wpuRGj92spGIw-rNPFcd9hwJaRVvfWdk,716
numpy/_core/tests/data/generate_umath_validation_data.cpp,sha256=BQakB5o8Mq60zex5ovVO0IatNa7xbF8JvXmtk6373So,5842
numpy/_core/tests/data/recarray_from_file.fits,sha256=NA0kliz31FlLnYxv3ppzeruONqNYkuEvts5wzXEeIc4,8640
numpy/_core/tests/data/umath-validation-set-README.txt,sha256=pxWwOaGGahaRd-AlAidDfocLyrAiDp0whf5hC7hYwqM,967
numpy/_core/tests/data/umath-validation-set-arccos.csv,sha256=yBlz8r6RnnAYhdlobzGGo2FKY-DoSTQaP26y8138a3I,61365
numpy/_core/tests/data/umath-validation-set-arccosh.csv,sha256=0GXe7XG1Z3jXAcK-OlEot_Df3MetDQSlbm3MJ__iMQk,61365
numpy/_core/tests/data/umath-validation-set-arcsin.csv,sha256=w_Sv2NDn-mLZSAqb56JT2g4bqBzxYAihedWxHuf82uU,61339
numpy/_core/tests/data/umath-validation-set-arcsinh.csv,sha256=DZrMYoZZZyM1DDyXNUxSlzx6bOgajnRSLWAzxcPck8k,60289
numpy/_core/tests/data/umath-validation-set-arctan.csv,sha256=0aosXZ-9DYTop0lj4bfcBNwYVvjZdW13hbMRTRRTmV0,60305
numpy/_core/tests/data/umath-validation-set-arctanh.csv,sha256=HEK9ePx1OkKrXIKkMUV0IxrmsDqIlgKddiI-LvF2J20,61339
numpy/_core/tests/data/umath-validation-set-cbrt.csv,sha256=v855MTZih-fZp_GuEDst2qaIsxU4a7vlAbeIJy2xKpc,60846
numpy/_core/tests/data/umath-validation-set-cos.csv,sha256=0PNnDqKkokZ7ERVDgbes8KNZc-ISJrZUlVZc5LkW18E,59122
numpy/_core/tests/data/umath-validation-set-cosh.csv,sha256=JKC4nKr3wTzA_XNSiQvVUq9zkYy4djvtu2-j4ZZ_7Oc,60869
numpy/_core/tests/data/umath-validation-set-exp.csv,sha256=rUAWIbvyeKh9rPfp2n0Zq7AKq_nvHpgbgzLjAllhsek,17491
numpy/_core/tests/data/umath-validation-set-exp2.csv,sha256=djosT-3fTpiN_f_2WOumgMuuKgC_XhpVO-QsUFwI6uU,58624
numpy/_core/tests/data/umath-validation-set-expm1.csv,sha256=K7jL6N4KQGX71fj5hvYkzcMXk7MmQes8FwrNfyrPpgU,60299
numpy/_core/tests/data/umath-validation-set-log.csv,sha256=ynzbVbKxFzxWFwxHnxX7Fpm-va09oI3oK1_lTe19g4w,11692
numpy/_core/tests/data/umath-validation-set-log10.csv,sha256=NOBD-rOWI_FPG4Vmbzu3JtX9UA838f2AaDFA-waiqGA,68922
numpy/_core/tests/data/umath-validation-set-log1p.csv,sha256=tdbYWPqWIz8BEbIyklynh_tpQJzo970Edd4ek6DsPb8,60303
numpy/_core/tests/data/umath-validation-set-log2.csv,sha256=39EUD0vFMbwyoXoOhgCmid6NeEAQU7Ff7QFjPsVObIE,68917
numpy/_core/tests/data/umath-validation-set-sin.csv,sha256=8PUjnQ_YfmxFb42XJrvpvmkeSpEOlEXSmNvIK4VgfAM,58611
numpy/_core/tests/data/umath-validation-set-sinh.csv,sha256=XOsBUuPcMjiO_pevMalpmd0iRv2gmnh9u7bV9ZLLg8I,60293
numpy/_core/tests/data/umath-validation-set-tan.csv,sha256=Hv2WUMIscfvQJ5Y5BipuHk4oE4VY6QKbQp_kNRdCqYQ,60299
numpy/_core/tests/data/umath-validation-set-tanh.csv,sha256=iolZF_MOyWRgYSa-SsD4df5mnyFK18zrICI740SWoTc,60299
numpy/_core/tests/examples/cython/checks.pyx,sha256=aGJS1WAuTIGtQpQxRK9SpXqlM0XFKn97giS3Pi2rt4Y,7344
numpy/_core/tests/examples/cython/meson.build,sha256=uuXVPKemNVMQ5MiEDqS4BXhwGHa96JHjS50WxZuJS_8,1268
numpy/_core/tests/examples/cython/setup.py,sha256=6k4eEMjzjXPhGAW440qpMp2S2l5Ltv-e9e-FnVnzl3w,857
numpy/_core/tests/examples/limited_api/limited_api1.c,sha256=htSR9ER3S8AJqv4EZMsrxQ-SufTIlXNpuFI6MXQs87w,346
numpy/_core/tests/examples/limited_api/limited_api2.pyx,sha256=1q4I59pdkCmMhLcYngN_XwQnPoLmDEo1uTGnhrLRjDc,203
numpy/_core/tests/examples/limited_api/limited_api_latest.c,sha256=ltBLbrl1g9XxD2wvN_-g3NhIizc8mxnh2Z6wCyXo-8E,452
numpy/_core/tests/examples/limited_api/meson.build,sha256=YM5RwW_waFymlWSHFhCCOHO6KCknooN0jCiqScL0i5M,1627
numpy/_core/tests/examples/limited_api/setup.py,sha256=p2w7F1ardi_GRXSrnNIR8W1oeH_pgmw_1P2wS0A2I6M,435
numpy/_core/tests/test__exceptions.py,sha256=PA9MhiaEITLOaIe86lnOwqAa3RFrA5Ra4IrqKXF-nMU,2881
numpy/_core/tests/test_abc.py,sha256=mIZtCZ8PEIOd6pxLqdUws3wMfXUjsVO3vOE9vK5YPd8,2221
numpy/_core/tests/test_api.py,sha256=D6x2gFFB_C7-SQ0dZhk_ecAz7We7wL0uiNHKNEmWzjo,22932
numpy/_core/tests/test_argparse.py,sha256=DRLQD5TxhudrQZ79hm5ds3eKsXh_Ub7QsvEYzsdDSX0,2824
numpy/_core/tests/test_array_api_info.py,sha256=zZvWezQ9raqNgO9gofXgYDcyXYT_wfIYtb3Wy-IjR_8,3062
numpy/_core/tests/test_array_coercion.py,sha256=wFskNvCBNB72SqcylcqVRAlAI6bim4gOtNhBdSqyVoM,34852
numpy/_core/tests/test_array_interface.py,sha256=9ND3Y00rgdBSgst5555zrzkvdWzZ4vZgWJOw3djXZAk,7767
numpy/_core/tests/test_arraymethod.py,sha256=gdRXJjnvAs6-QWZ7o18LX9cHdOIBvZ90neCWW1clRso,3264
numpy/_core/tests/test_arrayobject.py,sha256=cybY9FWXY34oapV9VWj9Lq4Yem-BbaFLu-NZcV7tuf0,790
numpy/_core/tests/test_arrayprint.py,sha256=qSa-PgHFjBmMAP9QqLY_a0enqfl_PvCWQ0KDSRJYDxg,47938
numpy/_core/tests/test_casting_floatingpoint_errors.py,sha256=nnBEgeRIENrOOZvTzRK7SRYYW9dD6E6npDmIuN0ggCc,5074
numpy/_core/tests/test_casting_unittests.py,sha256=jHnE_9O1YcquVLBzO9UANf83PUIph3CxVRmGOYEXp8M,34308
numpy/_core/tests/test_conversion_utils.py,sha256=fpduQ79yLpvZ8fdLs4H0CCsBEh3TlZs3SMr-lUQ6pTg,6605
numpy/_core/tests/test_cpu_dispatcher.py,sha256=nqlgFk-Ocfgc18g-b4fprYssfcpReiyvgbWPzsNEoFI,1552
numpy/_core/tests/test_cpu_features.py,sha256=zUqiY5TZwK201-SdccthhDg4UslQTC9ygP7Eo94wXbQ,15309
numpy/_core/tests/test_custom_dtypes.py,sha256=EjHB0Tl9sIoa5AJ4aGwotGDe5OP0nZEpBU4th2bn0VE,11633
numpy/_core/tests/test_cython.py,sha256=qnL1H7E8zu7s5b24s01gOQ6VA57Lazb9E7vuzUue8VU,8498
numpy/_core/tests/test_datetime.py,sha256=mxN5VHOyMv7KUYV5MKNVPx_vaifknpnr7w19qoph_9Y,117386
numpy/_core/tests/test_defchararray.py,sha256=tLrnS4oEVDwjbx74fHyi9r43yAE0J7mJZVfdeHvlSJg,30601
numpy/_core/tests/test_deprecations.py,sha256=YRpiqYQcwkHWSba42HRqiG5CXghqiji533i1rQivMsU,28674
numpy/_core/tests/test_dlpack.py,sha256=KMUlft-fmLF8tIHupr5W6griJ7GD5r-McqZEMWg5-Fw,5475
numpy/_core/tests/test_dtype.py,sha256=PSBPpWZc7-gYxq1O289DQ6LjNqt8kQyeb3EKFQ_boP8,78238
numpy/_core/tests/test_einsum.py,sha256=kLCN_rgcBsJ9d3P2TDZEQqzXKqNMhPxXWBps21YED3g,52944
numpy/_core/tests/test_errstate.py,sha256=fno3tnaY1U8uIgd7JZSgJoM_yWkd5GrHXDC6-PIYVhY,4646
numpy/_core/tests/test_extint128.py,sha256=tVrw3jMHQkA0ebk7Pnq33I5Yu9V24KNHotYIG3V8ds0,5644
numpy/_core/tests/test_function_base.py,sha256=0hHLDmLconROrue_DEQ06mwhGakmDAfZ2w23aXVsorY,17119
numpy/_core/tests/test_getlimits.py,sha256=mVtBnC0QtZKHf83a2gr-Y9aZ3NIr1JogWZ1vVQCfQXU,6738
numpy/_core/tests/test_half.py,sha256=VYPyap9GYOWZuphsfFofcIRl-oa5Ufrtv83OTp6azdU,24593
numpy/_core/tests/test_hashtable.py,sha256=Ws1EeQWCf7vz8G_VsFTIZUVI-hgKXUEAbtQpvoBjBHo,1147
numpy/_core/tests/test_indexerrors.py,sha256=wvatr7JlqAAYv-hHAAT-9DwUCnRcKiJ9qLcl6aKe9RU,4734
numpy/_core/tests/test_indexing.py,sha256=R6Hv-fSYst2lPbROX9Ak6FwogNghceuERlEDOhkNpj4,55064
numpy/_core/tests/test_item_selection.py,sha256=kI30kiX8mIrZYPn0jw3lGGw1ruZF4PpE9zw-aai9EPA,6458
numpy/_core/tests/test_limited_api.py,sha256=khmxJnnaPqquzKUg4tPbFkrdd0pGarg8VBjQQla5Tvg,2824
numpy/_core/tests/test_longdouble.py,sha256=H7VeOyaLfSMHClUDSKloOuHiDbZxeoypJnc5AtsM4xw,13890
numpy/_core/tests/test_machar.py,sha256=eDTrzJgwfaus0Ts86-HR9YkAPOwOSOPImPTHugn1EOc,1069
numpy/_core/tests/test_mem_overlap.py,sha256=SSe1tvi8BBAs-0osQd0y1IauR47Axsk7pK74x9GOWwU,29140
numpy/_core/tests/test_mem_policy.py,sha256=S1dznz5gBfEimb1kXlFsU6CDz22dPxZrnDS6kaIL8r4,16676
numpy/_core/tests/test_memmap.py,sha256=g-vr5Zys-NGc3IwK8UkqhkjSXYJx4d_enElyw6ydh6A,7743
numpy/_core/tests/test_multiarray.py,sha256=rcZ4Cs9YfnI62I1BQNw9hu8QyWjrBnT3p_T1AT_L3mI,390638
numpy/_core/tests/test_multithreading.py,sha256=oXqLo4FRQEw13VWN6rjMT8t4r-URNOr2Hoi_Asp0tIw,3552
numpy/_core/tests/test_nditer.py,sha256=L0qk1bdYAZ2lKjHLRJyAsOvqdr6cY-f96l0p4kP8QGk,131269
numpy/_core/tests/test_nep50_promotions.py,sha256=K9xRzpSpRK6H2sr0PWDCFDiF21yoJMDHhobcnSnP44g,12841
numpy/_core/tests/test_numeric.py,sha256=AoFQv2EE6LCZ98ayb5WhaeqHT6Ez040uG1wyp1CSTWI,157414
numpy/_core/tests/test_numerictypes.py,sha256=aADiXLPAkgAFF80_tRczhuH6lVyMLcA3k_AbGcDemp4,23292
numpy/_core/tests/test_overrides.py,sha256=Qq2dVlTwJiCb5t8kE_RN2xlpN3xwOO9L3WVc_8mQWAY,25686
numpy/_core/tests/test_print.py,sha256=mzUSbQ2kSa1aDl7NRUexj5UG4IM4zaZ-5EIoEoXhA_Q,6836
numpy/_core/tests/test_protocols.py,sha256=6pxSZKmde5KHoN3iEMKReAFHrMldAm3ZZQwVh_kQ9Uw,1189
numpy/_core/tests/test_records.py,sha256=0JJJ4FHRQq_9C7OimEo3ayfxYY3z4yeIyIkGMzfghlQ,20535
numpy/_core/tests/test_regression.py,sha256=z_Upeg9mqcd_506IxIpkDytljfv__jHzEN481GLK-f4,94869
numpy/_core/tests/test_scalar_ctors.py,sha256=3mhZlumKJs5WazhPgATWf5Y4E4POQy-bcUBSEt5pasc,6719
numpy/_core/tests/test_scalar_methods.py,sha256=A-3dotRg9kZ8CLEqnuNViTLORqMPoxWtkv1tIm00Gh4,8182
numpy/_core/tests/test_scalarbuffer.py,sha256=EdiF5tVrZXDchoK0P5sbQgluyyYQCIrLCaxvafaCKNk,5582
numpy/_core/tests/test_scalarinherit.py,sha256=Kq7KS2pF7m3kUYdxy5QfJbVRKFZvPVCZ5Wq3iAp4pdQ,2592
numpy/_core/tests/test_scalarmath.py,sha256=yXcE1_2HqoRbxRPTJmFKfiajksl3-85XHkPnFTAxbaw,46572
numpy/_core/tests/test_scalarprint.py,sha256=9ITKVAklqVuphseF1lfMFrv1pBHKNJvTJFZQw8NDhfY,18788
numpy/_core/tests/test_shape_base.py,sha256=4fWGEOtsEmX-VJMg84WyT8RILwJO4LbyyBHMtap50NA,31009
numpy/_core/tests/test_simd.py,sha256=cIKbJZbJY6Jh_4GbZOM6fPFG68c_rvsz-VH0-SHWuYU,48698
numpy/_core/tests/test_simd_module.py,sha256=g0XWjB1TE4E0y4McOjkZKhR7OB-K01eqy4pJcGfU2zg,3903
numpy/_core/tests/test_stringdtype.py,sha256=kSMIZ0sw6LbbrGG77vhNOrJyCccpZiG8bQ8_lWGk-lk,54918
numpy/_core/tests/test_strings.py,sha256=lIM5AborDEEdewMvRlW-WlmoFXKNR4LQdPgWzJoQJJE,48667
numpy/_core/tests/test_ufunc.py,sha256=t3Ns9ZUPk8HRR7lqCHi9UhYRHcI138cuIUAIVLQo2u4,131619
numpy/_core/tests/test_umath.py,sha256=o4W61y8p0ocAY1PRnPkFYYi3QoYxhXj_0D3jn9Zg8YM,192318
numpy/_core/tests/test_umath_accuracy.py,sha256=tj-RfH2NqnuCHjGjZhzyRFXmfYlLW9hvBq76h_DF_Yo,5450
numpy/_core/tests/test_umath_complex.py,sha256=pWRHpzBodvDGoKG1gkRAKJ1uPxQ_fV_VqIm77SD0BlA,23290
numpy/_core/tests/test_unicode.py,sha256=eAUcEFUGAfWASXdqTgW1sQ2mU9jnUlzWBhLgctKDo7Q,12868
numpy/_core/umath.py,sha256=Z4ytcJ5nuqjhS6LHYzqalqYz1CVJUED4DRMcEcyTwH0,2063
numpy/_distributor_init.py,sha256=IKy2THwmu5UgBjtVbwbD9H-Ap8uaUJoPJ2btQ4Jatdo,407
numpy/_expired_attrs_2_0.py,sha256=cnmE3ryrFo0CKf_gFhNu388jh055JgYAU6ah8r0aCrM,3913
numpy/_globals.py,sha256=XVuUPpFLueqKUTNwqiOjWWahnM-vGxGy4tYA3ph-EAE,3090
numpy/_pyinstaller/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/_pyinstaller/hook-numpy.py,sha256=Ood-XcWlQQkk90SY0yDg7RKsUFVGwas9TqI-Gbc58_s,1393
numpy/_pyinstaller/tests/__init__.py,sha256=IJtzzjPSw419P-c2T4OT48p-Zu4JohoF9svWqhDshgk,329
numpy/_pyinstaller/tests/pyinstaller-smoke.py,sha256=6iL-eHMQaG3rxnS5EgcvrCqElm9aKL07Cjr1FZJSXls,1143
numpy/_pyinstaller/tests/test_pyinstaller.py,sha256=8K-7QxmfoXCG0NwR0bhIgCNrDjGlrTzWnrR1sR8btgU,1135
numpy/_pytesttester.py,sha256=3PD0aJCA6x2VlfUr0oI63_dkuZXBuL23lCJ07zK5Ge0,6287
numpy/_pytesttester.pyi,sha256=OtyXSiuSy8o_78w3QNQRjMLpvvNyEdC0aMsx6T-vRxU,489
numpy/_typing/__init__.py,sha256=FzB-zSTTh4iB8zZfae9jYPgvKJPJq2YtT4ZxsHvjUdk,7093
numpy/_typing/_add_docstring.py,sha256=5T3jfL4n7l_xQwgk611rlRACRwYmLFLSXaI1zFRuEsw,3970
numpy/_typing/_array_like.py,sha256=qan8K7oV-jcy-vgUNaCdvX_wUYIMV7Iam-X_6qNBw4M,4693
numpy/_typing/_callable.pyi,sha256=81nH6Oq-tp7FyDSCe5-b63eU0wc72tF-GFx7zikewlg,12555
numpy/_typing/_char_codes.py,sha256=j-S7cfBXfxUPFsOgHYcaueimdd_tYJ2NJUwJS_pkM14,6980
numpy/_typing/_dtype_like.py,sha256=S2PQRxFn2EPUMyRGAANNCf6m4ZInIhm9SxJ4DCRXsQQ,5889
numpy/_typing/_extended_precision.py,sha256=dGios-1k-QBGew7YFzONZTzVWxz-aYAaqlccl2_h5Bo,777
numpy/_typing/_nbit.py,sha256=9WFXtFFjveTV-5qLDBXh8TYwOGTanix_k67OZWmc_FQ,361
numpy/_typing/_nested_sequence.py,sha256=5eNaVZAV9tZQLFWHYOuVs336JjoiaWxyZQ7cMKb6m1I,2566
numpy/_typing/_scalars.py,sha256=9v-1xahC9TZg28FTfBG15vWCcnDB1bfWz7ejT0eDrVw,1031
numpy/_typing/_shape.py,sha256=fY1qi6UDFjPW1b4GaxhcJ9tRAQu6SXLZINd_Vy60XSY,231
numpy/_typing/_ufunc.pyi,sha256=q_rcajMSJ8xi9jniUOSI-zAq24iJHWoepjhg3f-G_3Q,12127
numpy/_utils/__init__.py,sha256=Lsv7p1NzTQNaMG8vkYxvHPYDoMUolFzG1KdhGFZMedE,3224
numpy/_utils/_convertions.py,sha256=0xMxdeLOziDmHsRM_8luEh4S-kQdMoMg6GxNDDas69k,329
numpy/_utils/_inspect.py,sha256=8Ma7QBRwfSWKeK1ShJpFNc7CDhE6fkIE_wr1FxrG1A8,7447
numpy/_utils/_pep440.py,sha256=Vr7B3QsijR5p6h8YAz2LjNGUyzHUJ5gZ4v26NpZAKDc,14069
numpy/char/__init__.py,sha256=WGpEng-lsHKxUlmuANY8hKCl3ZC622HYSAFnpf7sgUE,93
numpy/char/__init__.pyi,sha256=HWtTk64fLvQGvS_2MFk_zKv1Kt1lD-7EQ8SG-tR39XM,1332
numpy/compat/__init__.py,sha256=b3rw1J_V3MwU-LZf8uISRKvfXzFaBjFHACbgyLo785Y,727
numpy/compat/py3k.py,sha256=Je74CVk_7qI_qX7pLbYcuQJsxlMq1poGIfRIrH99kZQ,3833
numpy/compat/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/conftest.py,sha256=XX6VW1nFUYiyC-Su3hZU8o1ZjJB0dT_prx29uo_7Xuo,8402
numpy/core/__init__.py,sha256=FWRkekGqZ1NF4YYNfm46mOAO9u3v4ZYts_lc8ygQfqY,1275
numpy/core/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/core/_dtype.py,sha256=3SnNsjxlKobD8Dn8B9egjIQuQLdbWz9OtVAZ4_wlDw8,322
numpy/core/_dtype_ctypes.py,sha256=lLzxauA8PVnopTuGh9USt1nVw2qCI8Z7bL66er3JoHU,350
numpy/core/_internal.py,sha256=f3eVtRx2tKrJxxavZNe_f1Ln-_1shhSlfeRZEDTlxhU,947
numpy/core/_multiarray_umath.py,sha256=Yb0HORec_wcEV3RNNU4RZnlATYTUQtjAHMYmL4pvNLs,2096
numpy/core/_utils.py,sha256=s57m7yaOneaUIljT4WrwqX-tqqexCIomSQKgeL10RIU,917
numpy/core/arrayprint.py,sha256=a1DkStlBSsVViSJw523Mm-lboVaAtCloBNCrigyOpbI,338
numpy/core/defchararray.py,sha256=G9S6jkdXegRkXl58hSpPnmndjdym4801Yzq2lzzmApM,346
numpy/core/einsumfunc.py,sha256=px-rSPkwAMbRNmp5uILgVC2QSr73InKFfvW7LSfNGGw,338
numpy/core/fromnumeric.py,sha256=aNquLnfZX1XZRAz5MJza5ZT7IlgJo0TMHlR62YT2biM,342
numpy/core/function_base.py,sha256=Sa9Ec2Y21kPmjn4Xsh7Y1V1c7bUdxYjzixIwHZJ4sCo,350
numpy/core/getlimits.py,sha256=aYJVaVqiSGKuPfSIa7r0MMZMQkJP2NRNJ7Zd2dszygU,334
numpy/core/multiarray.py,sha256=SwVF8KNm29qyaq7vx8rrljNNxfn0e6G5y1H830n1Rac,792
numpy/core/numeric.py,sha256=LSuzJ9OsQ0IEpW2rKlAwuvNypZeDZ0AJDoJOt93XB-k,359
numpy/core/numerictypes.py,sha256=RvhfWFh9KR0SPDNcrAYnW-PO9TKAND75ONXhL5Djs8Q,346
numpy/core/overrides.py,sha256=sWaAgbH_piO0mWDeVqqoqkFqqpPHM87FqOZFJ3AO8lU,334
numpy/core/records.py,sha256=j9BftQLLljVdcENT41eGflG7DA7miXQ7q3Yf53-zYcY,326
numpy/core/shape_base.py,sha256=MhuxPRwwg5hIdHcJ-LABdQ0oYEYGVxeD-aomaFs9-f4,338
numpy/core/umath.py,sha256=f6KbsWYh5oTj3_FWHip_dr51BdczTAtMqgpn9_eHcz4,318
numpy/ctypeslib.py,sha256=OGUL21cShz1-L-ukq7O22fQO_c3I20D0vHnTfPB3qo4,17626
numpy/ctypeslib.pyi,sha256=UIko2MzsafWZm8C3TUE5ujXhx_ODK19el6yJRGsDFn4,8052
numpy/doc/ufuncs.py,sha256=9xt8H34GhrXrFq9cWFUGvJFePa9YuH9Tq1DzAnm2E2E,5414
numpy/dtypes.py,sha256=zuPwgC0ijF2oDRAOJ6I9JKhaJuhXFAygByLQaoVtT54,1312
numpy/dtypes.pyi,sha256=fyJsHFlxw591c6KxT4telUuyHCfkMJREvDnV8cghiBA,14047
numpy/exceptions.py,sha256=CL6CCTFew1anOiTbpTsujjoV1Dd1ztFSJoxuDrTs0Mg,7874
numpy/exceptions.pyi,sha256=rc61wK_jQEfT7IZrlVZObnxuJ8KRgyPXAabGUwblsaE,639
numpy/f2py/__init__.py,sha256=aov9Lx4W4n19yV4gmvAi7Z1nMQJxDWnAdDAu81F44Dg,2526
numpy/f2py/__init__.pyi,sha256=eA7uYXZr0p0aaz5rBW-EypLx9RchrvqDYtSnkEJQsYw,1087
numpy/f2py/__main__.py,sha256=6i2jVH2fPriV1aocTY_dUFvWK18qa-zjpnISA-OpF3w,130
numpy/f2py/__version__.py,sha256=7HHdjR82FCBmftwMRyrlhcEj-8mGQb6oCH-wlUPH4Nw,34
numpy/f2py/_backends/__init__.py,sha256=7_bA7c_xDpLc4_8vPfH32-Lxn9fcUTgjQ25srdvwvAM,299
numpy/f2py/_backends/_backend.py,sha256=GKb9-UaFszT045vUgVukPs1n97iyyjqahrWKxLOKNYo,1187
numpy/f2py/_backends/_distutils.py,sha256=D9UkK_cvecPdqahGO-D0rck3luTPlyP7Trc3pV7eVIs,2388
numpy/f2py/_backends/_meson.py,sha256=AZqZ2FJQl3by26Nez85JrjpmmNSKb9nq3S7RaBRrpPs,8116
numpy/f2py/_backends/meson.build.template,sha256=hQeTapAY0xtni5Li-QaEtWx9DH9WDKah2lcEuSZfLLo,1599
numpy/f2py/_isocbind.py,sha256=zaBgpfPNRmxVG3doUIlbZIiyB990MsXiwDabrSj9HnQ,2360
numpy/f2py/_src_pyf.py,sha256=4t6TN4ZKWciC4f1z6fwaGrpIGhHKRiwHfcrNj4FIzCg,7654
numpy/f2py/auxfuncs.py,sha256=Nf2Ip9CYmVyhzhl9OrWs9lI48Kf7oHqwPtBw8HvBOME,26825
numpy/f2py/capi_maps.py,sha256=OmDkzbytC6ifqiF9RGe_mPIeyqIPC-xcE0t1DP_FaBA,30627
numpy/f2py/cb_rules.py,sha256=fSxXAxjNaPXt54E957v1-Q3oCM06vbST5gFu1D98ic4,25004
numpy/f2py/cfuncs.py,sha256=T5sxuNGU5NbUJRF3KkdfFoHn1MbVVSDOHxaM_qC-ZTg,52125
numpy/f2py/common_rules.py,sha256=gHB76WypbkVmhaD_RWhy8Od4zDTgj8cbDOdUdIp6PIQ,5131
numpy/f2py/crackfortran.py,sha256=XAyygoIAKQOmd9KATKNV3GC1B25XJpaD6tz0rRzeqXA,148023
numpy/f2py/diagnose.py,sha256=0SRXBE2hJgKJN_Rf4Zn00oKXC_Tka3efPWM47zg6BoY,5197
numpy/f2py/f2py2e.py,sha256=bT2E3LeTd0wU_LVnmpwBmfrvrafSXduVzZU3c9ezMq0,28941
numpy/f2py/f90mod_rules.py,sha256=zmL53Fftnah3Gu8699qsrks1yNjHu0oc9jOTqfYEOyw,9992
numpy/f2py/func2subr.py,sha256=6d2R5awuHRT4xzgfUfwS7JHTqhhAieSXcENlssD_2c4,10298
numpy/f2py/rules.py,sha256=HL9RNAQRXohvP5Zcr9sJ8r-NjRZYlG9tQW40WhOFQ7o,62874
numpy/f2py/setup.cfg,sha256=Fpn4sjqTl5OT5sp8haqKIRnUcTPZNM6MIvUJBU7BIhg,48
numpy/f2py/src/fortranobject.c,sha256=YPF0qUjOUnGdOFZEcvId1ooZMfmuDcGkaWbOG7_4HmM,46048
numpy/f2py/src/fortranobject.h,sha256=7cfRN_tToAQ1Na13VQ2Kzb2ujMHUAgGsbScnfLVOHqs,5823
numpy/f2py/symbolic.py,sha256=nm65BDeRr74QdIWjg6aFA8IkR_9uZHxRb6z8OcnqLU4,53268
numpy/f2py/tests/__init__.py,sha256=46XgeBE0seimp3wD4Ox0KutYeLwdsdRSiGECcG1iYu8,328
numpy/f2py/tests/src/abstract_interface/foo.f90,sha256=JFU2w98cB_XNwfrqNtI0yDTmpEdxYO_UEl2pgI_rnt8,658
numpy/f2py/tests/src/abstract_interface/gh18403_mod.f90,sha256=gvQJIzNtvacWE0dhysxn30-iUeI65Hpq7DiE9oRauz8,105
numpy/f2py/tests/src/array_from_pyobj/wrapmodule.c,sha256=s6XLwujiCr6Xi8yBkvLPBXRmo2WsGVohU7K9ALnKUng,7478
numpy/f2py/tests/src/assumed_shape/.f2py_f2cmap,sha256=But9r9m4iL7EGq_haMW8IiQ4VivH0TgUozxX4pPvdpE,29
numpy/f2py/tests/src/assumed_shape/foo_free.f90,sha256=oBwbGSlbr9MkFyhVO2aldjc01dr9GHrMrSiRQek8U64,460
numpy/f2py/tests/src/assumed_shape/foo_mod.f90,sha256=rfzw3QdI-eaDSl-hslCgGpd5tHftJOVhXvb21Y9Gf6M,499
numpy/f2py/tests/src/assumed_shape/foo_use.f90,sha256=rmT9k4jP9Ru1PLcGqepw9Jc6P9XNXM0axY7o4hi9lUw,269
numpy/f2py/tests/src/assumed_shape/precision.f90,sha256=r08JeTVmTTExA-hYZ6HzaxVwBn1GMbPAuuwBhBDtJUk,130
numpy/f2py/tests/src/block_docstring/foo.f,sha256=y7lPCPu7_Fhs_Tf2hfdpDQo1bhtvNSKRaZAOpM_l3dg,97
numpy/f2py/tests/src/callback/foo.f,sha256=C1hjfpRCQWiOVVzIHqnsYcnLrqQcixrnHCn8hd9GhVk,1254
numpy/f2py/tests/src/callback/gh17797.f90,sha256=_Nrl0a2HgUbtymGU0twaJ--7rMa1Uco2A3swbWvHoMo,148
numpy/f2py/tests/src/callback/gh18335.f90,sha256=NraOyKIXyvv_Y-3xGnmTjtNjW2Znsnlk8AViI8zfovc,506
numpy/f2py/tests/src/callback/gh25211.f,sha256=a2sxlQhtDVbYn8KOKHUYqwc-aCFt7sDPSnJsXFG35uI,179
numpy/f2py/tests/src/callback/gh25211.pyf,sha256=FWxo0JWQlw519BpZV8PoYeI_FZ_K6C-3Wk6gLrfBPlw,447
numpy/f2py/tests/src/cli/gh_22819.pyf,sha256=5rvOfCv-wSosB354LC9pExJmMoSHnbGZGl_rtA2fogA,142
numpy/f2py/tests/src/cli/hi77.f,sha256=ttyI6vAP3qLnDqy82V04XmoqrXNM6uhMvvLri2p0dq0,71
numpy/f2py/tests/src/cli/hiworld.f90,sha256=QWOLPrTxYQu1yrEtyQMbM0fE9M2RmXe7c185KnD5x3o,51
numpy/f2py/tests/src/common/block.f,sha256=GQ0Pd-VMX3H3a-__f2SuosSdwNXHpBqoGnQDjf8aG9g,224
numpy/f2py/tests/src/common/gh19161.f90,sha256=BUejyhqpNVfHZHQ-QC7o7ZSo7lQ6YHyX08lSmQqs6YM,193
numpy/f2py/tests/src/crackfortran/accesstype.f90,sha256=-5Din7YlY1TU7tUHD2p-_DSTxGBpDsWYNeT9WOwGhno,208
numpy/f2py/tests/src/crackfortran/data_common.f,sha256=ZSUAh3uhn9CCF-cYqK5TNmosBGPfsuHBIEfudgysun4,193
numpy/f2py/tests/src/crackfortran/data_multiplier.f,sha256=jYrJKZWF_59JF9EMOSALUjn0UupWvp1teuGpcL5s1Sc,197
numpy/f2py/tests/src/crackfortran/data_stmts.f90,sha256=19YO7OGj0IksyBlmMLZGRBQLjoE3erfkR4tFvhznvvE,693
numpy/f2py/tests/src/crackfortran/data_with_comments.f,sha256=hoyXw330VHh8duMVmAQZjr1lgLVF4zFCIuEaUIrupv0,175
numpy/f2py/tests/src/crackfortran/foo_deps.f90,sha256=CaH7mnWTG7FcnJe2vXN_0zDbMadw6NCqK-JJ2HmDjK8,128
numpy/f2py/tests/src/crackfortran/gh15035.f,sha256=jJly1AzF5L9VxbVQ0vr-sf4LaUo4eQzJguhuemFxnvg,375
numpy/f2py/tests/src/crackfortran/gh17859.f,sha256=7K5dtOXGuBDAENPNCt-tAGJqTfNKz5OsqVSk16_e7Es,340
numpy/f2py/tests/src/crackfortran/gh22648.pyf,sha256=qZHPRNQljIeYNwbqPLxREnOrSdVV14f3fnaHqB1M7c0,241
numpy/f2py/tests/src/crackfortran/gh23533.f,sha256=w3tr_KcY3s7oSWGDmjfMHv5h0RYVGUpyXquNdNFOJQg,126
numpy/f2py/tests/src/crackfortran/gh23598.f90,sha256=41W6Ire-5wjJTTg6oAo7O1WZfd1Ug9vvNtNgHS5MhEU,101
numpy/f2py/tests/src/crackfortran/gh23598Warn.f90,sha256=1v-hMCT_K7prhhamoM20nMU9zILam84Hr-imck_dYYk,205
numpy/f2py/tests/src/crackfortran/gh23879.f90,sha256=LWDJTYR3t9h1IsrKC8dVXZlBfWX7clLeU006X6Ow8oI,332
numpy/f2py/tests/src/crackfortran/gh2848.f90,sha256=gPNasx98SIf7Z9ibk_DHiGKCvl7ERtsfoGXiFDT7FbM,282
numpy/f2py/tests/src/crackfortran/operators.f90,sha256=-Fc-qjW1wBr3Dkvdd5dMTrt0hnjnV-1AYo-NFWcwFSo,1184
numpy/f2py/tests/src/crackfortran/privatemod.f90,sha256=7bubZGMIn7iD31wDkjF1TlXCUM7naCIK69M9d0e3y-U,174
numpy/f2py/tests/src/crackfortran/publicmod.f90,sha256=Pnwyf56Qd6W3FUH-ZMgnXEYkb7gn18ptNTdwmGan0Jo,167
numpy/f2py/tests/src/crackfortran/pubprivmod.f90,sha256=eYpJwBYLKGOxVbKgEqfny1znib-b7uYhxcRXIf7uwXg,165
numpy/f2py/tests/src/crackfortran/unicode_comment.f90,sha256=aINLh6GlfTwFewxvDoqnMqwuCNb4XAqi5Nj5vXguXYs,98
numpy/f2py/tests/src/f2cmap/.f2py_f2cmap,sha256=iUOtfHd3OuT1Rz2-yiSgt4uPKGvCt5AzQ1iygJt_yjg,82
numpy/f2py/tests/src/f2cmap/isoFortranEnvMap.f90,sha256=iJCD8a8MUTmuPuedbcmxW54Nr4alYuLhksBe1sHS4K0,298
numpy/f2py/tests/src/isocintrin/isoCtests.f90,sha256=jcw-fzrFh0w5U66uJYfeUW4gv94L5MnWQ_NpsV9y0oI,998
numpy/f2py/tests/src/kind/foo.f90,sha256=zIHpw1KdkWbTzbXb73hPbCg4N2Htj3XL8DIwM7seXpo,347
numpy/f2py/tests/src/mixed/foo.f,sha256=90zmbSHloY1XQYcPb8B5d9bv9mCZx8Z8AMTtgDwJDz8,85
numpy/f2py/tests/src/mixed/foo_fixed.f90,sha256=pxKuPzxF3Kn5khyFq9ayCsQiolxB3SaNtcWaK5j6Rv4,179
numpy/f2py/tests/src/mixed/foo_free.f90,sha256=fIQ71wrBc00JUAVUj_r3QF9SdeNniBiMw6Ly7CGgPWU,139
numpy/f2py/tests/src/modules/gh25337/data.f90,sha256=9Uz8CHB9i3_mjC3cTOmkTgPAF5tWSwYacG3MUrU-SY0,180
numpy/f2py/tests/src/modules/gh25337/use_data.f90,sha256=WATiDGAoCKnGgMzm_iMgmfVU0UKOQlk5Fm0iXCmPAkE,179
numpy/f2py/tests/src/modules/gh26920/two_mods_with_no_public_entities.f90,sha256=c7VU4SbK3yWn-6wksP3tDx_Hxh5u_g8UnlDpjU_-tBg,402
numpy/f2py/tests/src/modules/gh26920/two_mods_with_one_public_routine.f90,sha256=eEU7RgFPh-TnNXEuJFdtJmTF-wPnpbHLQhG4fEeJnag,403
numpy/f2py/tests/src/modules/module_data_docstring.f90,sha256=tDZ3fUlazLL8ThJm3VwNGJ75QIlLcW70NnMFv-JA4W0,224
numpy/f2py/tests/src/modules/use_modules.f90,sha256=UsFfx0B2gu_tS-H-BpLWed_yoMDl1kbydMIOz8fvXWA,398
numpy/f2py/tests/src/negative_bounds/issue_20853.f90,sha256=fdOPhRi7ipygwYCXcda7p_dlrws5Hd2GlpF9EZ-qnck,157
numpy/f2py/tests/src/parameter/constant_array.f90,sha256=KRg7Gmq_r3B7t3IEgRkP1FT8ve8AuUFWT0WcTlXoN5U,1468
numpy/f2py/tests/src/parameter/constant_both.f90,sha256=-bBf2eqHb-uFxgo6Q7iAtVUUQzrGFqzhHDNaxwSICfQ,1939
numpy/f2py/tests/src/parameter/constant_compound.f90,sha256=re7pfzcuaquiOia53UT7qNNrTYu2euGKOF4IhoLmT6g,469
numpy/f2py/tests/src/parameter/constant_integer.f90,sha256=nEmMLitKoSAG7gBBEQLWumogN-KS3DBZOAZJWcSDnFw,612
numpy/f2py/tests/src/parameter/constant_non_compound.f90,sha256=IcxESVLKJUZ1k9uYKoSb8Hfm9-O_4rVnlkiUU2diy8Q,609
numpy/f2py/tests/src/parameter/constant_real.f90,sha256=quNbDsM1Ts2rN4WtPO67S9Xi_8l2cXabWRO00CPQSSQ,610
numpy/f2py/tests/src/quoted_character/foo.f,sha256=WjC9D9171fe2f7rkUAZUvik9bkIf9adByfRGzh6V0cM,482
numpy/f2py/tests/src/regression/AB.inc,sha256=cSNxitwrjTKMiJzhY2AI5FaXJ5y9zDgA27x79jyoI6s,16
numpy/f2py/tests/src/regression/f77comments.f,sha256=bqTsmO8WuSLVFsViIV7Nj7wQbJoZ7IAA3d2tpRDKsnA,626
numpy/f2py/tests/src/regression/f77fixedform.f95,sha256=hcLZbdozMJ3V9pByVRp3RoeUvZgLMRLFctpZvxK2hTI,139
numpy/f2py/tests/src/regression/f90continuation.f90,sha256=_W1fj0wXLqT91Q14qpBnM3F7rJKaiSR8upe0mR6_OIE,276
numpy/f2py/tests/src/regression/incfile.f90,sha256=i7Y1zgMXR9bSxnjeYWSDGeCfsS5jiyn7BLb-wbwjz2U,92
numpy/f2py/tests/src/regression/inout.f90,sha256=CpHpgMrf0bqA1W3Ozo3vInDz0RP904S7LkpdAH6ODck,277
numpy/f2py/tests/src/return_character/foo77.f,sha256=WzDNF3d_hUDSSZjtxd3DtE-bSx1ilOMEviGyYHbcFgM,980
numpy/f2py/tests/src/return_character/foo90.f90,sha256=ULcETDEt7gXHRzmsMhPsGG4o3lGrcx-FEFaJsPGFKyA,1248
numpy/f2py/tests/src/return_complex/foo77.f,sha256=8ECRJkfX82oFvGWKbIrCvKjf5QQQClx4sSEvsbkB6A8,973
numpy/f2py/tests/src/return_complex/foo90.f90,sha256=c1BnrtWwL2dkrTr7wvlEqNDg59SeNMo3gyJuGdRwcDw,1238
numpy/f2py/tests/src/return_integer/foo77.f,sha256=_8k1evlzBwvgZ047ofpdcbwKdF8Bm3eQ7VYl2Y8b5kA,1178
numpy/f2py/tests/src/return_integer/foo90.f90,sha256=bzxbYtofivGRYH35Ang9ScnbNsVERN8-6ub5-eI-LGQ,1531
numpy/f2py/tests/src/return_logical/foo77.f,sha256=FxiF_X0HkyXHzJM2rLyTubZJu4JB-ObLnVqfZwAQFl8,1188
numpy/f2py/tests/src/return_logical/foo90.f90,sha256=9KmCe7yJYpi4ftkKOM3BCDnPOdBPTbUNrKxY3p37O14,1531
numpy/f2py/tests/src/return_real/foo77.f,sha256=ZTrzb6oDrIDPlrVWP3Bmtkbz3ffHaaSQoXkfTGtCuFE,933
numpy/f2py/tests/src/return_real/foo90.f90,sha256=gZuH5lj2lG6gqHlH766KQ3J4-Ero-G4WpOOo2MG3ohU,1194
numpy/f2py/tests/src/size/foo.f90,sha256=IlFAQazwBRr3zyT7v36-tV0-fXtB1d7WFp6S1JVMstg,815
numpy/f2py/tests/src/string/char.f90,sha256=ihr_BH9lY7eXcQpHHDQhFoKcbu7VMOX5QP2Tlr7xlaM,618
numpy/f2py/tests/src/string/fixed_string.f90,sha256=5n6IkuASFKgYICXY9foCVoqndfAY0AQZFEK8L8ARBGM,695
numpy/f2py/tests/src/string/gh24008.f,sha256=UA8Pr-_yplfOFmc6m4v9ryFQ8W9OulaglulefkFWD68,217
numpy/f2py/tests/src/string/gh24662.f90,sha256=-Tp9Kd1avvM7AIr8ZukFA9RVr-wusziAnE8AvG9QQI4,197
numpy/f2py/tests/src/string/gh25286.f90,sha256=2EpxvC-0_dA58MBfGQcLyHzpZgKcMf_W9c73C_Mqnok,304
numpy/f2py/tests/src/string/gh25286.pyf,sha256=GjgWKh1fHNdPGRiX5ek60i1XSeZsfFalydWqjISPVV8,381
numpy/f2py/tests/src/string/gh25286_bc.pyf,sha256=6Y9zU66NfcGhTXlFOdFjCSMSwKXpq5ZfAe3FwpkAsm4,384
numpy/f2py/tests/src/string/scalar_string.f90,sha256=ACxV2i6iPDk-a6L_Bs4jryVKYJMEGUTitEIYTjbJes4,176
numpy/f2py/tests/src/string/string.f,sha256=shr3fLVZaa6SyUJFYIF1OZuhff8v5lCwsVNBU2B-3pk,248
numpy/f2py/tests/src/value_attrspec/gh21665.f90,sha256=JC0FfVXsnB2lZHb-nGbySnxv_9VHAyD0mKaLDowczFU,190
numpy/f2py/tests/test_abstract_interface.py,sha256=SGJqspdwvo6JWjQ-VAifepzTo86cU5VNxe9E_sH4Yts,850
numpy/f2py/tests/test_array_from_pyobj.py,sha256=I4jCiXSMJ-boNX4LyXVjwdZtH-yTbyqhknke65wy15E,23783
numpy/f2py/tests/test_assumed_shape.py,sha256=FeaqtrWyBf5uyArcmI0D2e_f763aSMpgU3QmdDXe-tA,1466
numpy/f2py/tests/test_block_docstring.py,sha256=2WGCsNBxtH57BjAYyPAzUZgiBRYWAQpC9zODP02OZec,582
numpy/f2py/tests/test_callback.py,sha256=19lLfl2HmJpKUPaTu3mdtK9meJXx_nx9vXpIC6LmwuI,6560
numpy/f2py/tests/test_character.py,sha256=29o5PCGC_t5UsOwmxM4dL3k3IPZ6hpUD25VFSZk5N9E,21926
numpy/f2py/tests/test_common.py,sha256=VPsy0SLqbKaUGgDqesYXmjYuLpnPK-XyzseqmV5QnhM,641
numpy/f2py/tests/test_crackfortran.py,sha256=VfM7uwXxuf0hhemV8QAQI31RKHxMefL2I56C5we0CZU,15888
numpy/f2py/tests/test_data.py,sha256=sFaaYt8EdWu6hI2Kyg2gK38ug5XsCUkPOEkl6zarh58,2898
numpy/f2py/tests/test_docs.py,sha256=DXAAqi0DHa6S25R6zQ5VhtbZYJxBKvnrs5KVw-ZAUI8,1874
numpy/f2py/tests/test_f2cmap.py,sha256=-WnN0HlqiG9RPgc1P_KSLZvqgQ4wGYDf0lFcyfWOLfs,385
numpy/f2py/tests/test_f2py2e.py,sha256=-ZFQWaxCdsnv6y2nun3bVe_ND6WIFss0hmjE-jUROuw,27844
numpy/f2py/tests/test_isoc.py,sha256=kY7yg7Jtyn_RBlozwe6UpQvtwPbPcpTC0B27s2GRo7s,1428
numpy/f2py/tests/test_kind.py,sha256=mLoh2b9XAQpirywOw9_qEelgKMRdMIMUqkkhAhgpols,1793
numpy/f2py/tests/test_mixed.py,sha256=ie1hr7-3QlyYkUt3suPTCNOqUbcaawphSlRLHmU6czA,870
numpy/f2py/tests/test_modules.py,sha256=wli_Cq9FroWg9nnOZplGAd9L5OX49h_Z-e8PyVVnk0w,2299
numpy/f2py/tests/test_parameter.py,sha256=Nt6elnMp5zJ-lk0Yu-mwUdLzYwhCFNtPT7o0EVr5Ip8,4633
numpy/f2py/tests/test_pyf_src.py,sha256=eD0bZu_GWfoCq--wWqEKRf-F2h5AwoTyO6GMA9wJPr4,1135
numpy/f2py/tests/test_quoted_character.py,sha256=T6I2EyopdItKamcokG0ylvhT7krZYhBU6hF3UFIBr2g,476
numpy/f2py/tests/test_regression.py,sha256=PfYdtgKW-6pn5VjU4hi2VNMClSFNl_rzt5EkpNBWX1s,4737
numpy/f2py/tests/test_return_character.py,sha256=DP63vrF6bIV-QRBsJ1ZpPsKz-u906Ph8M6_biPEzBJs,1511
numpy/f2py/tests/test_return_complex.py,sha256=4vtpIYqAZZrbKYi3fnP7l_Zn42YnBbPwl8-eNfZOHHo,2415
numpy/f2py/tests/test_return_integer.py,sha256=qR8Ismf40Ml2impqjGzjL2i-CRyGTxXVEvzQQMkJfJo,1776
numpy/f2py/tests/test_return_logical.py,sha256=XCmp8E8I6BOeNYF59HjSFAdv1hM9WaDvl8UDS10_05o,2017
numpy/f2py/tests/test_return_real.py,sha256=rxgglxBljLavw3LzWCeT41mYYVhvkTMlQE5E2rfg_LI,3253
numpy/f2py/tests/test_semicolon_split.py,sha256=gi0I439sNF1x2dl4fnJmkFzhoGUpE7ni7_mJ8tQdH5c,1653
numpy/f2py/tests/test_size.py,sha256=q6YqQvcyqdXJeWbGijTiCbxyEG3EkPcvT8AlAW6RCMo,1164
numpy/f2py/tests/test_string.py,sha256=5xZOfdReoHnId0950XfmtfduPPfBbtMkzBoXMtygvMk,2962
numpy/f2py/tests/test_symbolic.py,sha256=28quk2kTKfWhKe56n4vINJ8G9weKBfc7HysMlE9J3_g,18341
numpy/f2py/tests/test_value_attrspec.py,sha256=SKi010iuwoWCH5fGfLDpjYrqOxKtSGZ6a0W1EKPnHj8,339
numpy/f2py/tests/util.py,sha256=jPct2y1RaUxY-Tt0-_9OhyhR7pjhbziiZIIyYCNL5Tg,12217
numpy/f2py/use_rules.py,sha256=3pTDOPur6gbPHPtwuMJPQvpnUMw39Law1KFSH0coB_0,3527
numpy/fft/__init__.py,sha256=cW8oJRorHlG10mhnhAB1OOkg4HpG2NGYHDgonFNI04s,8326
numpy/fft/__init__.pyi,sha256=IYRQ9v8fS2H9iMdCJfO9am_86vbcpWFpzXLbwPjSSZo,531
numpy/fft/_helper.py,sha256=Yvph-5gksd0HebLSXq4UKfVYOwSiqNIa4THpv0aA2HE,6775
numpy/fft/_helper.pyi,sha256=4Z9lTaHKEldkDrh8vpYJKOeGfPrTJAs9UATt1u8FlEw,1330
numpy/fft/_pocketfft.py,sha256=_HhbYRHUWykBONqrifnmx8MzCjp7c4vtEomxL-LNvUY,63168
numpy/fft/_pocketfft.pyi,sha256=JZJM0TeqqY0WrnYpr1mzfe3FWTa8yzEcrqEoTLJhGZc,2961
numpy/fft/_pocketfft_umath.cpython-312-x86_64-linux-gnu.so,sha256=GMq6MpylYoJQSHfdBdLT9pWskt6wRMQG2xzFD3UJGJQ,649272
numpy/fft/helper.py,sha256=str0NJ1vpLNlC_3vMfulTu9D9_cThxKG2zkaGuZ5NTY,610
numpy/fft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/fft/tests/test_helper.py,sha256=pVYVLUwNEcE9M8eyHaRi7JOgc6k5p_JVzJ0AKnelgvI,6149
numpy/fft/tests/test_pocketfft.py,sha256=xGbyPmmqizmAhlsiUW8K1NziI7ZFsr6TGeJuVAS4r0Q,24412
numpy/lib/__init__.py,sha256=Nfa0hj1MaQAl4HcI2Xs3c49Idmg3T1qZ14MI1g-DUkY,3187
numpy/lib/__init__.pyi,sha256=f6_weeFha_MBO1TfKATaqRKHv3N9n5A2azKB7jd_9UY,770
numpy/lib/_array_utils_impl.py,sha256=eMGdZi7auu6201h4v4eQZ2miF8KmdMGDApbBFgRE-6Q,1689
numpy/lib/_array_utils_impl.pyi,sha256=w37sTX4F4AJNrN4pnUovcqEcxfsdkpdhF4CDtsbB36o,748
numpy/lib/_arraypad_impl.py,sha256=dLDDK2K6tFa7YIpCn3hUfNJs5k4raJE5jGIR3zPIp60,32408
numpy/lib/_arraypad_impl.pyi,sha256=ADXphtAORYl3EqvE5qs_u32B_TALKSOtF43jOLmoxRw,1728
numpy/lib/_arraysetops_impl.py,sha256=y2oL_wfZ9c0lOAtavTFDUO4Kc0LCvtLh5O7K1JWstFs,39540
numpy/lib/_arraysetops_impl.pyi,sha256=eHCK7aprLhBsPCaeyhnZyzDuNCX2jW7cQBvOmON55z8,9315
numpy/lib/_arrayterator_impl.py,sha256=d_Rhl-pERrhxSuCxRNPIIle9b0WoMvVNhkupPn0BIaE,7168
numpy/lib/_arrayterator_impl.pyi,sha256=M-oDG2ShygvxufQG0NMimXYMJtTQcCPHPwogWO1bN00,1514
numpy/lib/_datasource.py,sha256=FJ7k1HghREU7udh8ZuO5ZZF3nHJfOkj7iWijhoVFqIQ,22729
numpy/lib/_function_base_impl.py,sha256=3C7KxtRV7iiYULVDK24BTmsy8uwJeCnNJNkhOZEs_ow,194622
numpy/lib/_function_base_impl.pyi,sha256=JNyxgAPGbwFcq8kGXRCdN3gXYzui0hLalmxtMoNXfz8,18763
numpy/lib/_histograms_impl.py,sha256=HR7WHm6OJ1kSO7Xe6RsvXGSMkm6ZoeRvhw-xz-325K4,38658
numpy/lib/_histograms_impl.pyi,sha256=hfWyE2pyRJcijx0qsZYXNjJ3PvApbPPNjzTa-u25bVs,1001
numpy/lib/_index_tricks_impl.py,sha256=R42AzmmnRKVEs0PQZdQ8WW26rKs-eqrdwHLf4quCPDE,32305
numpy/lib/_index_tricks_impl.pyi,sha256=udLQYETv5PGTWWIGvaUMhHE22DA1kYWmlkk40043BqU,4163
numpy/lib/_iotools.py,sha256=mMhxeGBt-T8prjWpNhn_xvZCj6u6OWWmmsvKP6vbM5w,30941
numpy/lib/_nanfunctions_impl.py,sha256=P0j6zikzsvWaK2FPRauhxc9HbOWUqnbwj2vqnb1WYwc,72643
numpy/lib/_nanfunctions_impl.pyi,sha256=WI7OtJWk9HLWpYSw5hufi3q-sCvscZxNByar7fBkM5U,613
numpy/lib/_npyio_impl.py,sha256=NmDSSrU52p3IAtqmvauquFhrmOfrOEd_JYMFOPzVRHs,98977
numpy/lib/_npyio_impl.pyi,sha256=BGMto5xQF78N-NXKpllg-9CqxXdYoo7yboHobbaK1PA,10259
numpy/lib/_polynomial_impl.py,sha256=6rD5Cy4mSDk2CsuAdJOq2he-PSa-ZiqsdgyyQAF5qx0,44294
numpy/lib/_polynomial_impl.pyi,sha256=hwookIwMJ8VrQWaZhjcJb8nunseXfm_Z1nlJ2W9Xdng,6936
numpy/lib/_scimath_impl.py,sha256=rB2YoGQUv7G8l8MBO-BBGf-ygZZ6Ek2Z2IwxcMBOLvc,15526
numpy/lib/_scimath_impl.pyi,sha256=E2roKJzMFwWSyhLu8UPUr54WOpxF8jp_pyXYBgsUSQ8,2883
numpy/lib/_shape_base_impl.py,sha256=ZV_oyGh8rj7rjQjnmnVqyF7SByu5mz-7A7Y43vElRJI,39647
numpy/lib/_shape_base_impl.pyi,sha256=JGwVgkKV16P0a5iDAPYrq1Z8tY0JvF6MWwsHDNCW9HQ,4723
numpy/lib/_stride_tricks_impl.py,sha256=fUKDv5PfDh-CjnIYlccDPgQ8tD5oV4YS8YG4dvNB1XQ,18202
numpy/lib/_stride_tricks_impl.pyi,sha256=J_DcfNwm4btn7ynKMAdDGHDSNRbxuWlQKsbGLat3T9o,1753
numpy/lib/_twodim_base_impl.py,sha256=dXJBvQwpwBPgGybEK5KZTscRs5TD4P7lqi8SL6e1jHY,33698
numpy/lib/_twodim_base_impl.pyi,sha256=HGRS44kgPKp1gJisZZ4hBDxT3wuBldsR8dgPypbFcko,10845
numpy/lib/_type_check_impl.py,sha256=QRKyUp1CY_560_ZmUoIyOdRuVul9UJ1l7t8fEDH_ZRw,19350
numpy/lib/_type_check_impl.pyi,sha256=hRKacjRatDTGfYhJYYnfpFwj3uHgQJuVpRGUdcoqkJ4,5207
numpy/lib/_ufunclike_impl.py,sha256=empYifZ0_BxOBpTFVmb7X6cMjX4ilBl0a398nzBD75E,6342
numpy/lib/_ufunclike_impl.pyi,sha256=vxaQ_C9VeH9OzAxotaBTwPNu247jo3tAbwq4O6xPBHA,1299
numpy/lib/_user_array_impl.py,sha256=n36wSKrwpatZVc7CBZSXLZhyUYqy5XKemnZ4gBjpaUE,7890
numpy/lib/_utils_impl.py,sha256=8eRQqHw5i-Y9nnFmdE0yhhKW5xxCwuGacxiY1ktTxM8,23404
numpy/lib/_utils_impl.pyi,sha256=beIHx7IktQu13uqwpOJ0dz5P6CtYV_MN8xREYAfqxA4,644
numpy/lib/_version.py,sha256=eWioqEi0TN4fC6db-sfbP9f9VC7YMs6AjR_Qgzg02SY,4853
numpy/lib/_version.pyi,sha256=B572hyWrUWG-TAAAXrNNAT4AgyUAmJ4lvgpwMkDzunk,633
numpy/lib/array_utils.py,sha256=zoaLw9TvrAFRkh9n8uMyr8kvug3IvVlUT7LcJzB3Tk0,130
numpy/lib/array_utils.pyi,sha256=kEO5wShp8zEbNTPu-Kw-EHuZQvq1rXHzgjK797xCV0Q,191
numpy/lib/format.py,sha256=EBKCaCtsDHIjPOb04fhFXI2tT4eqn4q1FSqDpeBXgfE,36310
numpy/lib/format.pyi,sha256=YWBxC3GdsZ7SKBN8I7nMwWeVuFD1aT9d-VJ8zE4-P-o,748
numpy/lib/introspect.py,sha256=Jg78l8sYniZzPhAPGRmqa0_BLTb-VjghzA5oEqmRSqE,2737
numpy/lib/mixins.py,sha256=jedTdqltOakWU1sZ_GY_oFHmFJP7Q3JbONo5ZRpJNJY,7365
numpy/lib/mixins.pyi,sha256=yJM9NNPaU1-TQ3D9vj9QgcY3L_lDKEjI6a8Y9NWylzo,3114
numpy/lib/npyio.py,sha256=NCxqWedJbSM5M-wr69TED8x7KXcyBJ0x5u49vj4sPkI,62
numpy/lib/npyio.pyi,sha256=b_cbxg8tD8AA9ql9mqMCcA0Wts5iUBCXSpVNBLNzvZ0,92
numpy/lib/recfunctions.py,sha256=GPJsQ0whE27grE_Ifre-5-mt1IQYVDDUSaWaWEvK_Vw,59908
numpy/lib/scimath.py,sha256=iO0IiDgpHk1EurdUvJIE2KqDzVOfvSsU3MFIlJskIOE,118
numpy/lib/scimath.pyi,sha256=MIWKfkv7MVE063prnzdSzI8pnVHPnXIFWOhBwlL6_0U,241
numpy/lib/stride_tricks.py,sha256=VGR5M8Jyw8IC4S6XEB9NN_GULTJJQj_1QrItIi_BJiM,82
numpy/lib/stride_tricks.pyi,sha256=Fqn9EZXdjIgUTce6UMD7rBBb8289QTMzohhjHwYP3TU,124
numpy/lib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/lib/tests/data/py2-np0-objarr.npy,sha256=ZLoI7K3iQpXDkuoDF1Ymyc6Jbw4JngbQKC9grauVRsk,258
numpy/lib/tests/data/py2-objarr.npy,sha256=F4cyUC-_TB9QSFLAo2c7c44rC6NUYIgrfGx9PqWPSKk,258
numpy/lib/tests/data/py2-objarr.npz,sha256=xo13HBT0FbFZ2qvZz0LWGDb3SuQASSaXh7rKfVcJjx4,366
numpy/lib/tests/data/py3-objarr.npy,sha256=7mtikKlHXp4unZhM8eBot8Cknlx1BofJdd73Np2PW8o,325
numpy/lib/tests/data/py3-objarr.npz,sha256=vVRl9_NZ7_q-hjduUr8YWnzRy8ESNlmvMPlaSSC69fk,453
numpy/lib/tests/data/python3.npy,sha256=X0ad3hAaLGXig9LtSHAo-BgOvLlFfPYMnZuVIxRmj-0,96
numpy/lib/tests/data/win64python2.npy,sha256=agOcgHVYFJrV-nrRJDbGnUnF4ZTPYXuSeF-Mtg7GMpc,96
numpy/lib/tests/test__datasource.py,sha256=65KXfUUvp8wXSqgQisuYlkhg-qHjBV5FXYetL8Ba-rc,10571
numpy/lib/tests/test__iotools.py,sha256=W2gLNsi2S8-4qixUs6EKkTYnOOp55qLLuM3zpBzZoR4,13744
numpy/lib/tests/test__version.py,sha256=aO3YgkAohLsLzCNQ7vjIwdpFUMz0cPLbcuuxIkjuN74,1999
numpy/lib/tests/test_array_utils.py,sha256=vOC6AmlPIQbVxQf2DiRL02May5IK5BK2GUFK0nP83FM,1119
numpy/lib/tests/test_arraypad.py,sha256=pPTksDqBqZT9TnzRS8mjhBykFBcib-n0Xc4GWR1lqWE,56087
numpy/lib/tests/test_arraysetops.py,sha256=0s5uYlB-WgsnIboqiROFBHD-9BcfPVNtee8QhEpCHK4,38012
numpy/lib/tests/test_arrayterator.py,sha256=AYs2SwV5ankgwnvKI9RSO1jZck118nu3SyZ4ngzZNso,1291
numpy/lib/tests/test_format.py,sha256=b3mu6GruBY9uQJmAAThnbQ4I4zBdaz6v0zBU5wmXbJw,40951
numpy/lib/tests/test_function_base.py,sha256=UbcsDvZXdSsm5oazvxFwrwi_Al-O82hOLNarvc4l1FI,167324
numpy/lib/tests/test_histograms.py,sha256=3GfHDMqAihBcbU6qoki_G8dSOzBbD5jU1SColm6i8Sg,33554
numpy/lib/tests/test_index_tricks.py,sha256=ZpKsvd3P3p2hwfj6sHlL_lysJp1IevAoM6AdpeTAx8M,20368
numpy/lib/tests/test_io.py,sha256=TpKQ5q_ClEZxcU9VRDi50kBIjSn6ST5B5qazz1Igp60,109391
numpy/lib/tests/test_loadtxt.py,sha256=1fsmA0b9d6BVIzj5gWnagGKYjmmXAnAY1W2eyw1XnN8,39499
numpy/lib/tests/test_mixins.py,sha256=Wivwz3XBWsEozGzrzsyyvL3qAuE14t1BHk2LPm9Z9Zc,7030
numpy/lib/tests/test_nanfunctions.py,sha256=e8IxqRADPgtHzjZpIZUFbtxYoB1-xBHnfaNMg8lyNWc,53351
numpy/lib/tests/test_packbits.py,sha256=2QaNYKH29cVD-S4YYBIQBd1xQ9bc2OqHdZT6yS7Txjk,17544
numpy/lib/tests/test_polynomial.py,sha256=XwAkZbKZaF2yyeEjbo0WevOwewGWPaox-8liLZZ4YnU,11400
numpy/lib/tests/test_recfunctions.py,sha256=6jzouPEQ7Uhtj8_-W5yTI6ymNp2nLgmdHzxdd74jVuM,44001
numpy/lib/tests/test_regression.py,sha256=XufkjEqWF1qqS7m76tqvg4Za8yW04jEz_QaKNphZPuU,7714
numpy/lib/tests/test_shape_base.py,sha256=W1q-tgBENS19wpOKSzEi63OSjatE4qC1viQG22qoacE,27488
numpy/lib/tests/test_stride_tricks.py,sha256=9g25TXSGLsvfeIrlkQ8l1fx_pZ48b4dxCzXXUbsKC5g,22997
numpy/lib/tests/test_twodim_base.py,sha256=ll-72RhqCItIPB97nOWhH7H292h4nVIX_w1toKTPMUg,18841
numpy/lib/tests/test_type_check.py,sha256=kh2n-xjjmygmul0L0vMvmcXySX9ViBjWGKJYQbN6yCA,14702
numpy/lib/tests/test_ufunclike.py,sha256=5AFySuvUfggh0tpBuQHJ7iZRrP0r_yZZv5xHxOuCZ1s,3023
numpy/lib/tests/test_utils.py,sha256=zzgwQGId2P8RUgimSsm7uMCYb61xPenrP_N0kcZU8x4,2374
numpy/lib/user_array.py,sha256=Ev3yeNNLZVNWk9xZuiCIbODYKwQ6XfYGpI5WAoYvtok,49
numpy/linalg/__init__.py,sha256=XNtdLo33SVTjQbXeimLFa5ZudzpEEwnfJBNorVbxuyc,2106
numpy/linalg/__init__.pyi,sha256=WeROFcujp1o9g_5HJ5j1DSXSMn-3ZCp1NlkqBhfHOMg,960
numpy/linalg/_linalg.py,sha256=8Acn4v42LxIr1QwhYeRYZkB2WU-YGNQEpE4aCzhjYxI,115454
numpy/linalg/_linalg.pyi,sha256=BCWPfxz8dx4nYfR7QBB_PvAoy57yCHp8E5SnzhpAJnE,10725
numpy/linalg/_umath_linalg.cpython-312-x86_64-linux-gnu.so,sha256=jDPmeuwAHiaNEF-bq_fBghkGfByYXzahz18D4MSpqq8,227657
numpy/linalg/lapack_lite.cpython-312-x86_64-linux-gnu.so,sha256=Bo_XdZSmtl5S_SO28xUH3Q8kF8ygPT94v6CtYdzFTZg,30009
numpy/linalg/linalg.py,sha256=JQWcEvjY_bjhaMHXY5vDk69OIoMzX5Rvbn1eGW2FCvE,584
numpy/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/linalg/tests/test_deprecations.py,sha256=9p_SRmtxj2zc1doY9Ie3dyy5JzWy-tCQWFoajcAJUmM,640
numpy/linalg/tests/test_linalg.py,sha256=wKnbuMyOqJ1s_NVPA6xXaYjJxNIL3fHhh114cheflVg,83355
numpy/linalg/tests/test_regression.py,sha256=-iM74Wagl4_vZ2CPFG_IBt0UEJpAYvsd30sf3QZsOY4,6705
numpy/ma/API_CHANGES.txt,sha256=F_4jW8X5cYBbzpcwteymkonTmvzgKKY2kGrHF1AtnrI,3405
numpy/ma/LICENSE,sha256=BfO4g1GYjs-tEKvpLAxQ5YdcZFLVAJoAhMwpFVH_zKY,1593
numpy/ma/README.rst,sha256=krf2cvVK_zNQf1d3yVYwg0uDHzTiR4vHbr91zwaAyoI,9874
numpy/ma/__init__.py,sha256=iv-YxXUZe4z7W53QZWY0ndicV43AGsIygArsoN3tQb8,1419
numpy/ma/__init__.pyi,sha256=HQBOppzm8lvEENgI3k6DtGT5eB9nuaiuNeKa-2jjLqQ,6041
numpy/ma/core.py,sha256=jz7OnJEyOrxUn64EGvrroGs-cvNTyYAaMc7zblJUYRk,287660
numpy/ma/core.pyi,sha256=3shisCCbyuDiejyRR_wIgoVLHuK4jbILGVpVtKpcKT4,14301
numpy/ma/extras.py,sha256=_1rMjo--D-8yL5mq9Ri88RtO38DpIoPCH2DTVN3ppjc,71023
numpy/ma/extras.pyi,sha256=8VHhU_A5uaULPhmnXbQgrgWBQx9R7ejmTUCYFEyx3w4,2653
numpy/ma/mrecords.py,sha256=0hMM8idHDAtZaqnSpfkEVQ97i9Kr3y9crCCKeQFiY58,27194
numpy/ma/mrecords.pyi,sha256=xRZj2cS2HlZ1mSBm2DZKJH4WKn1cbP5EaHw-GfQpsP0,1884
numpy/ma/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/ma/tests/test_arrayobject.py,sha256=MSvEcxlsVt4YZ7mVXU8q_hkwM0I7xsxWejEqnUQx6hE,1099
numpy/ma/tests/test_core.py,sha256=B1ISKIABRG1YadaP59SkSiOVVMrt39ar2Mau4ZlDXG8,215883
numpy/ma/tests/test_deprecations.py,sha256=nq_wFVt2EBHcT3AHxattfKXx2JDf1K5D-QBzUU0_15A,2566
numpy/ma/tests/test_extras.py,sha256=h0Zc0u4dXlQ3E0qADNYlH7iF4XX3K2A6HiY5hseRwSs,78314
numpy/ma/tests/test_mrecords.py,sha256=-nFjKUNYG_-gJ6RpZbWnx_TJlmkRAagA7AnVaf9YJfI,19855
numpy/ma/tests/test_old_ma.py,sha256=BW01_4m8wZcHvAkZ8FIjDmFfusnjgFmGVbRyqbWD000,32753
numpy/ma/tests/test_regression.py,sha256=foMpI0luAvwkkRpAfPDV_810h1URISXDZhmaNhxb50k,3287
numpy/ma/tests/test_subclassing.py,sha256=X8HylxMtWb55lCVslO0-w7URLgRIL5oK_Ahh9vMdTPg,17026
numpy/ma/testutils.py,sha256=sbiHivmwPQX3fPAPUe9OMktEqrwg1rcr8xgKfMM1Ex0,10272
numpy/ma/timer_comparison.py,sha256=Gm5zQYF_X8IEMdKBSnS4mFMi2wmojyftUa-iLg4lwcU,15694
numpy/matlib.py,sha256=hBmpfUQRZuNUQdvBFqlr4ZpX5gnODbaSkojNgbV4gAE,10691
numpy/matrixlib/__init__.py,sha256=BHBpQKoQv4EjT0UpWBA-Ck4L5OsMqTI2IuY24p-ucXk,242
numpy/matrixlib/__init__.pyi,sha256=WAYa7HoOr3wIRWLWg2Of80HlEfSJbGlCLXC1y0DO9k8,232
numpy/matrixlib/defmatrix.py,sha256=4Sat_jwWLiwlm5h1m4um0l0Uz2KspmHJQHUhvnZ4hoI,30756
numpy/matrixlib/defmatrix.pyi,sha256=lmBMRahKcMOl2PHDo79J67VRAZOkI54BzfDaTLpE0LI,451
numpy/matrixlib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/matrixlib/tests/test_defmatrix.py,sha256=tLHvsnn2xIKLLZULYqhQ1IJOtSdS52BfOOhU8-7jjvA,15035
numpy/matrixlib/tests/test_interaction.py,sha256=jiLmXS0JtwEx0smkb5hUnY5Slp9I8FwGlYGHKE3iG1w,11895
numpy/matrixlib/tests/test_masked_matrix.py,sha256=1x3mzFol1GYvVxKXcmRYLi-On3cmK7gEjSVEyvbkh-w,8914
numpy/matrixlib/tests/test_matrix_linalg.py,sha256=ObbSUXU4R2pWajH__xAdizADrU2kBKDDCxkDV-oVBXc,2059
numpy/matrixlib/tests/test_multiarray.py,sha256=jB3XCBmAtcqf-Wb9PwBW6uIykPpMPthuXLJ0giTKzZE,554
numpy/matrixlib/tests/test_numeric.py,sha256=MP70qUwgshTtThKZaZDp7_6U-Z66NIV1geVhasGXejQ,441
numpy/matrixlib/tests/test_regression.py,sha256=LBkm6_moDjuU9RY4FszgaknOj3IyCp3t-Ej3HJfqpdk,932
numpy/polynomial/__init__.py,sha256=XNK7ZWsBECCoHnJZ0NqKiF1ErZqvdxszE1NJ6Hc2Vz0,6760
numpy/polynomial/__init__.pyi,sha256=u2ZJqcaXqDtkpAELYqvJyXR9GDP7nY8g1RSf5uLUX5w,611
numpy/polynomial/_polybase.py,sha256=YYhJc7aRY8loDBaeUVOYJycxJIHvHAExxCDWE89Orh0,39892
numpy/polynomial/_polybase.pyi,sha256=yACuOVhbb_t3FXojkgFGVoZPubuEE-g82AKjVFNb5nQ,8760
numpy/polynomial/_polytypes.pyi,sha256=-FuvgcwdO3ZvBH4kg2Oa1j7qBvGdpoqYaBHDNsOlfCU,22891
numpy/polynomial/chebyshev.py,sha256=KvqDOupsi0yfPHund77cWt8LW6pYHuhwSBsMCx7_hgQ,62939
numpy/polynomial/chebyshev.pyi,sha256=9cJoCeRvzHuunQoCEy2pGOUdCp0KU65q7Tb8pTqLvGU,4725
numpy/polynomial/hermite.py,sha256=tulZ27-NhQS9IM7tmfsUZjyIoXQ5tpLSbAwDnd875qY,55058
numpy/polynomial/hermite.pyi,sha256=dm1gYq04GxQu5T4N5LqTYbZblLoXDqZDs6CtmycCU3w,2445
numpy/polynomial/hermite_e.py,sha256=RC0JYrI4GtFtIHPtsfGZ8tehfaYyjpJq4vJQr5P0kIg,52808
numpy/polynomial/hermite_e.pyi,sha256=klpXixSq5MRTlh6AlN1jRXPDXcnRdgUZPTxQjZpFKhM,2537
numpy/polynomial/laguerre.py,sha256=thRzmp-2OS6z006Iailq10LgQxmIvLqNPcELfWnAOZI,52941
numpy/polynomial/laguerre.pyi,sha256=QiCFjYZRAuYaty8LelfOvomgal1xFU9-4oKL68l1jyc,2174
numpy/polynomial/legendre.py,sha256=9dqxsvoQRA-BFvv_UULt6yXSHh77yyVEZkoTlEdTl3Y,51597
numpy/polynomial/legendre.pyi,sha256=SaQ9PZG50KF4g0iQd6B-xYOBz1vTDGtI4wChAINlFZY,2173
numpy/polynomial/polynomial.py,sha256=hc-KSTR21tuOvkrIjGT4DGjucPmKJxnVQE1pRf_mE3w,52699
numpy/polynomial/polynomial.pyi,sha256=Y4yeYfi879s5_Xm3SqdRmhQhbgJJBRRbajhCj1irTSw,2002
numpy/polynomial/polyutils.py,sha256=JhrI05cXg0nGNu-C1X2Q7L5OKUdvl8S23pu7r-AqI-c,22486
numpy/polynomial/polyutils.pyi,sha256=XYAYqUmjZVS_49uDszZE3SNI_lxJgx1SkjqqBVDrz44,10426
numpy/polynomial/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/polynomial/tests/test_chebyshev.py,sha256=6tMsFP1h7K8Zf72mNOta6Tv52_fVTlXknseuffj080c,20522
numpy/polynomial/tests/test_classes.py,sha256=Tf6p3qCINxOfh7hsOdVp81-CJPkqNg1HnH2smcWbRBw,18450
numpy/polynomial/tests/test_hermite.py,sha256=N9b2dx2UWPyja5v02dSoWYPnKvb6H-Ozgtrx-xjWz2k,18577
numpy/polynomial/tests/test_hermite_e.py,sha256=_A3ohAWS4HXrQG06S8L47dImdZGTwYosCXnoyw7L45o,18911
numpy/polynomial/tests/test_laguerre.py,sha256=BZOgs49VBXOFBepHopxuEDkIROHEvFBfWe4X73UZhn8,17511
numpy/polynomial/tests/test_legendre.py,sha256=b_bblHs0F_BWw9ESuSq52ZsLKcQKFR5eqPf_SppWFqo,18673
numpy/polynomial/tests/test_polynomial.py,sha256=-NGrdiaM24zQUn9ugtAViwFtbVQ34qgErXTQ5KF-Fd8,22022
numpy/polynomial/tests/test_polyutils.py,sha256=ULZMU2soHOZ4uO0eJoRjxNkT3yGURuX35MXx1Bg5Wyk,3772
numpy/polynomial/tests/test_printing.py,sha256=x5JzVg3yfub38RkKhBG_zzlIxXkcRn0mkbTECG1Bc90,21333
numpy/polynomial/tests/test_symbol.py,sha256=Hg-V7jR7qz5FKg_DrlkaiFcCI1UujYFUJfpf2TuoJZM,5372
numpy/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/random/LICENSE.md,sha256=EDFmtiuARDr7nrNIjgUuoGvgz_VmuQjxmeVh_eSa8Z8,3511
numpy/random/__init__.pxd,sha256=9JbnX540aJNSothGs-7e23ozhilG6U8tINOUEp08M_k,431
numpy/random/__init__.py,sha256=81Thnexg5umN5WZwD5TRyzNc2Yp-d14B6UC7NBgVKh8,7506
numpy/random/__init__.pyi,sha256=2SxvWDuxTcI2gcgIAU4A-cG_Azq8QV-EPi8SScO4D2w,2123
numpy/random/_bounded_integers.cpython-312-x86_64-linux-gnu.so,sha256=6MnJ4twolJ5qUUQESqMVhqtyEQZ5zPB93n4fKW6Vkcs,340264
numpy/random/_bounded_integers.pxd,sha256=SH_FwJDigFEInhdliSaNH2H2ZIZoX02xYhNQA81g2-g,1678
numpy/random/_common.cpython-312-x86_64-linux-gnu.so,sha256=9wtY85VWu1k8azl9Ai-zDzpu1PP2ElXsjicopkJjdGo,263144
numpy/random/_common.pxd,sha256=7kGArYkBcemrxJcSttwvtDGbimLszdQnZdNvPMgN5xQ,4982
numpy/random/_examples/cffi/extending.py,sha256=xSla3zWqxi6Hj48EvnYfD3WHfE189VvC4XsKu4_T_Iw,880
numpy/random/_examples/cffi/parse.py,sha256=Bnb7t_6S_c5-3dZrQ-XX9EazOKhftUfcCejXXWyd1EU,1771
numpy/random/_examples/cython/extending.pyx,sha256=4IE692pq1V53UhPZqQiQGcIHXDoNyqTx62x5a36puVg,2290
numpy/random/_examples/cython/extending_distributions.pyx,sha256=oazFVWeemfE0eDzax7r7MMHNL1_Yofws2m-c_KT2Hbo,3870
numpy/random/_examples/cython/meson.build,sha256=GxZZT_Lu3nZsgcqo_7sTR_IdMJaHA1fxyjwrQTcodPs,1694
numpy/random/_examples/numba/extending.py,sha256=Ipyzel_h5iU_DMJ_vnXUgQC38uMDMn7adUpWSeEQLFE,1957
numpy/random/_examples/numba/extending_distributions.py,sha256=M3Rt9RKupwEq71JjxpQFbUO7WKSOuLfR1skRM2a-hbI,2036
numpy/random/_generator.cpython-312-x86_64-linux-gnu.so,sha256=89hDJvLcIKk5yQMTHubX0B7DbB1OtkBJjCZY4vg4hhI,1026488
numpy/random/_generator.pyi,sha256=10sKaoew5r7dEJYlpESLdpKROrz9h_iEFOTgBZ1PesU,24608
numpy/random/_mt19937.cpython-312-x86_64-linux-gnu.so,sha256=UOv9Uxn6jSvhViveLpQ8IGa9rQoRdJztziGTM-J1_nU,145160
numpy/random/_mt19937.pyi,sha256=WWnxy1KiYOun55nB0du7jArWKmJd5GTcltt_L9sPivA,724
numpy/random/_pcg64.cpython-312-x86_64-linux-gnu.so,sha256=B1Op6IwznXmlTpDVu5QekHPWDDhEX-iXgCjv4RBnuZY,147256
numpy/random/_pcg64.pyi,sha256=uxr5CbEJetN6lv9vBG21jlRhuzOK8SQnXrwqAQBxj_c,1091
numpy/random/_philox.cpython-312-x86_64-linux-gnu.so,sha256=0rd0DEILPgbT_WaDLembIyLGaDbXBVrZcr-lnHfqUDg,128120
numpy/random/_philox.pyi,sha256=6OGeH8PMjzs9t21IdQJAz-5eKkjY4jX0vYgv3bkCwGw,954
numpy/random/_pickle.py,sha256=4iS9ofvvuD0KKMtRpZEdBslH79blhK8wtjqxeWN_gcE,2743
numpy/random/_sfc64.cpython-312-x86_64-linux-gnu.so,sha256=UOfgT-GvvqE_AZjpaMdHCIQrrealoQzXFIRN3L8nA6k,93024
numpy/random/_sfc64.pyi,sha256=xscekcSRmOwEAmMIwJUUTBYNgYO1I-PN8-JLKJamKLc,631
numpy/random/bit_generator.cpython-312-x86_64-linux-gnu.so,sha256=LWcBGrZxPSK_dm47cuDkHtjfdWEdG8zGjnjJ85T36sA,245336
numpy/random/bit_generator.pxd,sha256=lArpIXSgTwVnJMYc4XX0NGxegXq3h_QsUDK6qeZKbNc,1007
numpy/random/bit_generator.pyi,sha256=EkXmABq21fF-wiCFmAehXLOSMQJk0fYyEJefzchBi0Q,3595
numpy/random/c_distributions.pxd,sha256=7DE-mV3H_Dihk4OK4gMHHkyD4tPX1cAi4570zi5CI30,6344
numpy/random/lib/libnpyrandom.a,sha256=-1eNSFrUGkCTqr47fgZpBAXE8Qa0kpQAYOlGJJctVWw,72270
numpy/random/mtrand.cpython-312-x86_64-linux-gnu.so,sha256=ljFJUgV4KNQWAfTXqq0RP-GSm9vaLPF0qa5xTskoLK0,814184
numpy/random/mtrand.pyi,sha256=4xzN5Ep8hN_NnOCBRI5PTOSv0gpGwSeev43AN23JXSA,22441
numpy/random/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/random/tests/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/random/tests/data/generator_pcg64_np121.pkl.gz,sha256=EfQ-X70KkHgBAFX2pIPcCUl4MNP1ZNROaXOU75vdiqM,203
numpy/random/tests/data/generator_pcg64_np126.pkl.gz,sha256=fN8deNVxX-HELA1eIZ32kdtYvc4hwKya6wv00GJeH0Y,208
numpy/random/tests/data/mt19937-testset-1.csv,sha256=Xkef402AVB-eZgYQkVtoxERHkxffCA9Jyt_oMbtJGwY,15844
numpy/random/tests/data/mt19937-testset-2.csv,sha256=nsBEQNnff-aFjHYK4thjvUK4xSXDSfv5aTbcE59pOkE,15825
numpy/random/tests/data/pcg64-testset-1.csv,sha256=xB00DpknGUTTCxDr9L6aNo9Hs-sfzEMbUSS4t11TTfE,23839
numpy/random/tests/data/pcg64-testset-2.csv,sha256=NTdzTKvG2U7_WyU_IoQUtMzU3kEvDH39CgnR6VzhTkw,23845
numpy/random/tests/data/pcg64dxsm-testset-1.csv,sha256=vNSUT-gXS_oEw_awR3O30ziVO4seNPUv1UIZ01SfVnI,23833
numpy/random/tests/data/pcg64dxsm-testset-2.csv,sha256=uylS8PU2AIKZ185OC04RBr_OePweGRtvn-dE4YN0yYA,23839
numpy/random/tests/data/philox-testset-1.csv,sha256=SedRaIy5zFadmk71nKrGxCFZ6BwKz8g1A9-OZp3IkkY,23852
numpy/random/tests/data/philox-testset-2.csv,sha256=dWECt-sbfvaSiK8-Ygp5AqyjoN5i26VEOrXqg01rk3g,23838
numpy/random/tests/data/sfc64-testset-1.csv,sha256=iHs6iX6KR8bxGwKk-3tedAdMPz6ZW8slDSUECkAqC8Q,23840
numpy/random/tests/data/sfc64-testset-2.csv,sha256=FIDIDFCaPZfWUSxsJMAe58hPNmMrU27kCd9FhCEYt_k,23833
numpy/random/tests/data/sfc64_np126.pkl.gz,sha256=MVa1ylFy7DUPgUBK-oIeKSdVl4UYEiN3AZ7G3sdzzaw,290
numpy/random/tests/test_direct.py,sha256=4QsNnYfZZtVq-uiLKnsCOdp3yk_bsLDjIFwC3UGdACs,19251
numpy/random/tests/test_extending.py,sha256=h9qrU2o5MxaxfzU2aO8x9VCgcyTReWApOXDIfE6uAL0,3988
numpy/random/tests/test_generator_mt19937.py,sha256=2-kLPE1yPSo-SMuBoTNtbYoTvJLecI3GxUy2wlInHGo,117294
numpy/random/tests/test_generator_mt19937_regressions.py,sha256=r2wzyXTRfyVk__f2PO9yKPRdwx5ez671OQyAglMfPpc,8094
numpy/random/tests/test_random.py,sha256=i44DXCHEBtKtOzwSBfADh_kBSjMPgaCJYHdFfs6sfCQ,70150
numpy/random/tests/test_randomstate.py,sha256=Cp-op2kfopZ8wq-SBQ12Mh5RQ0p8mcBQHYSh0h-DegU,85275
numpy/random/tests/test_randomstate_regression.py,sha256=xS_HOwtijRdgq-gZn0IDUcm0NxdjjJXYv6ex8WN7FPU,7999
numpy/random/tests/test_regression.py,sha256=RbAzZYLfyzUKmup5uJR19sK2N17L_d1rLRy-CWjtIaQ,5462
numpy/random/tests/test_seed_sequence.py,sha256=GNRJ4jyzrtfolOND3gUWamnbvK6-b_p1bBK_RIG0sfU,3311
numpy/random/tests/test_smoke.py,sha256=CsXvEgv1T3wvCAH6qYu8RCWoQOaI4_gm7aWNhAS4QRg,28174
numpy/rec/__init__.py,sha256=w2G_npkmqm5vrWgds8V6Gusehmi1bRbiqCxsl9yOjow,83
numpy/rec/__init__.pyi,sha256=02w4lxCiiwaw1V4hb35x3xn0Xd0Tz83mJGSsYGovxtc,297
numpy/strings/__init__.py,sha256=-hT1HYpbswLkRWswieJQwAYn72IAwuaSCA5S1sdSPMk,83
numpy/strings/__init__.pyi,sha256=Ki81NEwY5NABr-qIDvAvI3vVbDzCBJ9THlGJK3Zcdbw,1210
numpy/testing/__init__.py,sha256=InpVKoDAzMKO_l_HNcatziW_u1k9_JZze__t2nybrL0,595
numpy/testing/__init__.pyi,sha256=cYNKSlLuYnm6T1_qJlxRQMzxk9LfIglDSSZFGGTultw,1654
numpy/testing/_private/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/testing/_private/extbuild.py,sha256=r5Coglv6iICgnKSesD-BUEY3ZhGAfmzx_qBfDSMgiqo,8115
numpy/testing/_private/utils.py,sha256=-SAtWc-QwJA1VuGcWtw21LQnrgjjPVeWdnyiLDdORVQ,93573
numpy/testing/_private/utils.pyi,sha256=SXeZE5z-ROrww1w84V5weWd7YLVVKhsoBZRuy_T2JNg,10229
numpy/testing/overrides.py,sha256=IB0inJ_540YOcATsjm0Qy8jEvrY_mHRI5fQj-yI6Z6Q,2125
numpy/testing/print_coercion_tables.py,sha256=v9RlpFnOlaw34QGWnDIovDGhG1clwGhha0UnCqni0RE,6223
numpy/testing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/testing/tests/test_utils.py,sha256=2iTpm6fC_7QPYWyINCTchagXtW9i8pd8ZbdwKRsse68,70461
numpy/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/tests/test__all__.py,sha256=L3mCnYPTpzAgNfedVuq9g7xPWbc0c1Pot94k9jZ9NpI,221
numpy/tests/test_configtool.py,sha256=lhtwsoUPSOSdgnSdxvrvS4roiid86eWzSrGjdrKkH7g,1555
numpy/tests/test_ctypeslib.py,sha256=c0x56qlAMnxTCO9MiuV05LCoqju8cidHj1URV5gOwQE,12351
numpy/tests/test_lazyloading.py,sha256=YETrYiDLAqLX04K_u5_3NVxAfxDoeguxwkIRfz6qKcY,1162
numpy/tests/test_matlib.py,sha256=gwhIXrJJo9DiecaGLCHLJBjhx2nVGl6yHq80AOUQSRM,1852
numpy/tests/test_numpy_config.py,sha256=qHvepgi9oyAbQuZD06k7hpcCC2MYhdzcY6D1iQDPNMI,1241
numpy/tests/test_numpy_version.py,sha256=2d0EtPJZYP3XRE6C6rfJW6QsPlFoDxqgO1yPxObaiE0,1754
numpy/tests/test_public_api.py,sha256=B2vX4UjxpgqZrB-FBmD8ynu3cVHA8za8j7iQpPrr3v8,22829
numpy/tests/test_reloading.py,sha256=sGu5XM-_VCNphyJcY5VCoQCmy5MgtL6_hDnsqf2j_ro,2367
numpy/tests/test_scripts.py,sha256=jluCLfG94VM1cuX-5RcLFBli_yaJZpIvmVuMxRKRJrc,1645
numpy/tests/test_warnings.py,sha256=HOqWSVu80PY-zacrgMfzPF0XPqEC24BNSw6Lmvw32Vg,2346
numpy/typing/__init__.py,sha256=ph9_WtDCJ7tKrbbRcz5OZEbXwxRXZfzSd2K1mLab910,5267
numpy/typing/mypy_plugin.py,sha256=r53CPvn4IujSWOAnub_InD44qc90-XmSpUSXOIhWZ74,6409
numpy/typing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/typing/tests/data/fail/arithmetic.pyi,sha256=HKbBj3T6tw0jkJ-QOtm6zZDaYf7637F38fGbj2UJk8g,3778
numpy/typing/tests/data/fail/array_constructors.pyi,sha256=SjiwoGrefYsuScJcBQZlwzfvENADeRMxHYCDCo685Vc,1129
numpy/typing/tests/data/fail/array_like.pyi,sha256=OVAlEJZ5k8ZRKt0aGpZQwIjlUGpy0PzOOYqfI-IMqBQ,455
numpy/typing/tests/data/fail/array_pad.pyi,sha256=57oK0Yp53rtKjjIrRFYLcxa-IfIGhtI-bEem7ggJKwI,132
numpy/typing/tests/data/fail/arrayprint.pyi,sha256=f27hi9dyYgh3JbEuvFroYwTKU2a7ZUKUcOEgqiZLioo,590
numpy/typing/tests/data/fail/arrayterator.pyi,sha256=Qb7oMI1GdDQO_jcoJEAsMkXLjzOdcb3sx-b5mW73cAE,470
numpy/typing/tests/data/fail/bitwise_ops.pyi,sha256=gJ-ZL-e-yMbHMRKUv8r2KqJ08Mkgpg74nUe6lgi2RDU,583
numpy/typing/tests/data/fail/char.pyi,sha256=Zi3dygeaxHT8-5aFNCAreGU-T89zLg5pcE6c9NBCs6c,2712
numpy/typing/tests/data/fail/chararray.pyi,sha256=cWyxV7TuJVUHlhxgkmktqZSzt8KghctCc3eOfOvsK5g,2306
numpy/typing/tests/data/fail/comparisons.pyi,sha256=YrcL2POtM1g8GEWW4AJMl9vAkV-lG_6kEb7FzueeiLU,822
numpy/typing/tests/data/fail/constants.pyi,sha256=IzmswvmTKbAOkCjgyxu1jChlikIwqeAETHGVH2TtY0k,85
numpy/typing/tests/data/fail/datasource.pyi,sha256=gACpSdzMDej9WZbNvDQlkWX9DvHD7DjucesbH0EWEaM,405
numpy/typing/tests/data/fail/dtype.pyi,sha256=OAGABqdXNB8gClJFEGMckoycuZcIasMaAlS2RkiKROI,334
numpy/typing/tests/data/fail/einsumfunc.pyi,sha256=*******************************************,487
numpy/typing/tests/data/fail/false_positives.pyi,sha256=Q61qMsSsNCtmO0EMRxHj5Z7RYTyrELVpkzfJY5eK8Z0,366
numpy/typing/tests/data/fail/flatiter.pyi,sha256=JcggwDkKcMWDBz0Ky8-dkJzjwnKxQ-kyea5br5DDqq0,866
numpy/typing/tests/data/fail/fromnumeric.pyi,sha256=57E3-vHlXpN9YC6jkpBbAlHaydbRlgK49LZ019IM68Y,5591
numpy/typing/tests/data/fail/histograms.pyi,sha256=yAPVt0rYTwtxnigoGT-u7hhKCE9iYxsXc24x2HGBrmA,367
numpy/typing/tests/data/fail/index_tricks.pyi,sha256=moINir9iQoi6Q1ZuVg5BuSB9hSBtbg_uzv-Qm_lLYZk,509
numpy/typing/tests/data/fail/lib_function_base.pyi,sha256=0Wb5Dy0A2NRXWIBrjZ3Gsu6XkulJUu1lHECw5tUkVUI,1950
numpy/typing/tests/data/fail/lib_polynomial.pyi,sha256=Ur7Y4iZX6WmoH5SDm0ePi8C8LPsuPs2Yr7g7P5O613g,899
numpy/typing/tests/data/fail/lib_utils.pyi,sha256=6oI_kPhJqL0P0q-rsC3WtGso3V-hF7ntbNUmbhUPfXE,96
numpy/typing/tests/data/fail/lib_version.pyi,sha256=7-ZJDZwDcB-wzpMN8TeYtZAgaqc7xnQ8Dnx2ISiX2Ts,158
numpy/typing/tests/data/fail/linalg.pyi,sha256=yDd05aK1dI37RPt3pD2eJYo4dZFaT2yB1PEu3K0y9Tg,1322
numpy/typing/tests/data/fail/memmap.pyi,sha256=HSTCQYNuW1Y6X1Woj361pN4rusSPs4oDCXywqk20yUo,159
numpy/typing/tests/data/fail/modules.pyi,sha256=_ek4zKcdP-sIh_f-IDY0tP-RbLORKCSWelM9AOYxsyA,670
numpy/typing/tests/data/fail/multiarray.pyi,sha256=1_9X7BW6hukiappz0kn3WCWN6OWXtT6OQqmJmJpdkfQ,1643
numpy/typing/tests/data/fail/ndarray.pyi,sha256=cgoWlpQqBQ5pkfiYsoz2f6o-DASrVRCraKBCgXLJQSk,404
numpy/typing/tests/data/fail/ndarray_misc.pyi,sha256=VaPL5mDB0OkHpDslehpIG_420O5-qDGNnLMCMJfwQVo,1333
numpy/typing/tests/data/fail/nditer.pyi,sha256=w7emjnOxnf3NcvLktNLlke6Cuivn2gU3sVmGCfbG6rw,325
numpy/typing/tests/data/fail/nested_sequence.pyi,sha256=em4GZwLDFE0QSxxg081wVwhh-Dmtkn8f7wThI0DiXVs,427
numpy/typing/tests/data/fail/npyio.pyi,sha256=ix5WhSp3vKf7UZE5X6h_Cs1gOIhzisHpMoOCfEg8Dpo,518
numpy/typing/tests/data/fail/numerictypes.pyi,sha256=jl_pxMAq_VmkaK13-sfhUOUYGAQ4OV2pQ1d7wG-DNZg,120
numpy/typing/tests/data/fail/random.pyi,sha256=0sFOsJeHwYc1cUNF-MByWONEF_MP8CQWTjdyGFvgl90,2821
numpy/typing/tests/data/fail/rec.pyi,sha256=Ws3TyesnoQjt7Q0wwtpShRDJmZCs2jjP17buFMomVGA,704
numpy/typing/tests/data/fail/scalars.pyi,sha256=hr5uVhIa0DcpRxKleFTgUmsrN6XXoxM-uhQsV2_ZYn0,2951
numpy/typing/tests/data/fail/shape.pyi,sha256=pSxiQ6Stq60xGFKOGZUsisxIO0y4inJ8UpKeio89K04,137
numpy/typing/tests/data/fail/shape_base.pyi,sha256=Y_f4buHtX2Q2ZA4kaDTyR8LErlPXTzCB_-jBoScGh_Q,152
numpy/typing/tests/data/fail/stride_tricks.pyi,sha256=IjA0Xrnx0lG3m07d1Hjbhtyo1Te5cXgjgr5fLUo4LYQ,315
numpy/typing/tests/data/fail/strings.pyi,sha256=l4Q3_tyER2UESkzKZPyVpaIweuzbYzYyR1drV_k_eFk,2842
numpy/typing/tests/data/fail/testing.pyi,sha256=e7b5GKTWCtKGoB8z2a8edsW0Xjl1rMheALsvzEJjlCw,1370
numpy/typing/tests/data/fail/twodim_base.pyi,sha256=eRFtqBbwkVI6G6MZMVpep1UKnFMDYzhrN82fO3ilnH0,898
numpy/typing/tests/data/fail/type_check.pyi,sha256=CIyI0j0Buxv0QgCvNG2urjaKpoIZ-ZNawC2m6NzGlbo,379
numpy/typing/tests/data/fail/ufunc_config.pyi,sha256=0t_yJ4eVOhneDSfa3EsoTh6RreyMtkHVOi9oQ35_EW0,734
numpy/typing/tests/data/fail/ufunclike.pyi,sha256=JsJ3M8QZv9-6GKwRnojJGIfeIkdtJFe-3ix5reLXx-M,627
numpy/typing/tests/data/fail/ufuncs.pyi,sha256=8N8m_GbRAH0bWjDEzYnH4MREX86iBD46Ug9mm-vc1co,476
numpy/typing/tests/data/fail/warnings_and_errors.pyi,sha256=KXExnFGz9O7Veut_U7YEIpi6x-BdfeaGtpqWf1Yd274,185
numpy/typing/tests/data/misc/extended_precision.pyi,sha256=bS8bBeCFqjgtOiy-8_y39wfa7rwhdjLz2Vmo-RXAYD4,884
numpy/typing/tests/data/mypy.ini,sha256=KLojiWL5k-6Aj-b6Y3kyv2q4OoK3L5M7z2g5VT5Cor0,168
numpy/typing/tests/data/pass/arithmetic.py,sha256=hTtW4N1AdLDZNHCqK1--td9RQbEysSaWx9TIjmfvUo0,7532
numpy/typing/tests/data/pass/array_constructors.py,sha256=rfJ8SRB4raElxRjsHBCsZIkZAfqZMie0VE8sSKMgkHg,2447
numpy/typing/tests/data/pass/array_like.py,sha256=Pptxuxfy0ccmLo47LTA8VFohJh6rXxl8V5Xsm1ym_Q8,1018
numpy/typing/tests/data/pass/arrayprint.py,sha256=y_KkuLz1uM7pv53qfq7GQOuud4LoXE3apK1wtARdVyM,766
numpy/typing/tests/data/pass/arrayterator.py,sha256=FqcpKdUQBQ0FazHFxr9MsLEZG-jnJVGKWZX2owRr4DQ,393
numpy/typing/tests/data/pass/bitwise_ops.py,sha256=FmEs_sKaU9ox-5f0NU3_TRIv0XxLQVEZ8rou9VNehb4,964
numpy/typing/tests/data/pass/comparisons.py,sha256=0H6YT3cRqKkWNwX-VXJPZG3Xh6xvjkogADQH8zInk0c,2993
numpy/typing/tests/data/pass/dtype.py,sha256=YDuYAb0oKoJc9eOnKJuoPfLbIKOgEdE04_CYxRS4U5I,1070
numpy/typing/tests/data/pass/einsumfunc.py,sha256=eXj5L5MWPtQHgrHPsJ36qqrmBHqct9UoujjJCvHnF1k,1370
numpy/typing/tests/data/pass/flatiter.py,sha256=0BnbuLMBC7MQlprNZ0QhNSscfYwPhEhXOhWoyiRACWU,174
numpy/typing/tests/data/pass/fromnumeric.py,sha256=d_hVLyrVDFPVx33aqLIyAGYYQ8XAJFIzrAsE8QCoof4,3991
numpy/typing/tests/data/pass/index_tricks.py,sha256=oaFD9vY01_RI5OkrXt-xTk1n_dd-SpuPp-eZ58XR3c8,1492
numpy/typing/tests/data/pass/lib_utils.py,sha256=bj1sEA4gsmezqbYdqKnVtKzY_fb64w7PEoZwNvaaUdA,317
numpy/typing/tests/data/pass/lib_version.py,sha256=HnuGOx7tQA_bcxFIJ3dRoMAR0fockxg4lGqQ4g7LGIw,299
numpy/typing/tests/data/pass/literal.py,sha256=ObHVl2_Ja7-DSR-2FfuFwW8US-O0opZmChPmIDhQzgw,1360
numpy/typing/tests/data/pass/ma.py,sha256=slJZQFGPI4I13qc-CRfreEGhIUk4TdFk-Pv75yWanNM,171
numpy/typing/tests/data/pass/mod.py,sha256=owFL1fys3LPTWpAlsjS-IzW4sSu98ncp2BnsIetLSrA,1576
numpy/typing/tests/data/pass/modules.py,sha256=g9PhyLO6rflYHZtmryx1VWTubphN4TAPUSfoiYriTqE,625
numpy/typing/tests/data/pass/multiarray.py,sha256=MxHax6l94yqlTVZleAqG77ILEbW6wU5osPcHzxJ85ns,1331
numpy/typing/tests/data/pass/ndarray_conversion.py,sha256=d7cFNUrofdLXh9T_9RG3Esz1XOihWWQNlz5Lb0yt6dM,1525
numpy/typing/tests/data/pass/ndarray_misc.py,sha256=UOZZdelG843BuO11QAvfnB2W6NRA0aCsVnCoukQJTxk,2619
numpy/typing/tests/data/pass/ndarray_shape_manipulation.py,sha256=37eYwMNqMLwanIW9-63hrokacnSz2K_qtPUlkdpsTjo,640
numpy/typing/tests/data/pass/numeric.py,sha256=bn0SCWOUKks0LOa8xIV8LwoXh9M_ERQsKl68vez8aBM,1531
numpy/typing/tests/data/pass/numerictypes.py,sha256=6x6eN9-5NsSQUSc6rf3fYieS2poYEY0t_ujbwgF9S5Q,331
numpy/typing/tests/data/pass/random.py,sha256=LOycKHYsF1F0OvXC6W0rfdwuDGT0p1xypdqbsF4mU6Q,61810
numpy/typing/tests/data/pass/scalars.py,sha256=7jy6s0VuTPnGxlqZOLdPicefyKt3SAzdC2sHtONrMzk,3393
numpy/typing/tests/data/pass/shape.py,sha256=ZilyhS3fdV49sPUJAsdBuumMayULMc0syF0WD0NaKQY,369
numpy/typing/tests/data/pass/simple.py,sha256=kQEQK59ISrra5i7p-_ijdRq9OdCdV0l7GEalDQtC1VQ,2737
numpy/typing/tests/data/pass/simple_py3.py,sha256=HuLrc5aphThQkLjU2_19KgGFaXwKOfSzXe0p2xMm8ZI,96
numpy/typing/tests/data/pass/ufunc_config.py,sha256=uzXOhCl9N4LPV9hV2Iqg_skgkKMbBPBF0GXPU9EMeuE,1205
numpy/typing/tests/data/pass/ufunclike.py,sha256=U4Aay11VALvm22bWEX0eDWuN5qxJlg_hH5IpOL62M3I,1125
numpy/typing/tests/data/pass/ufuncs.py,sha256=1Rem_geEm4qyD3XaRA1NAPKwr3YjRq68zbIlC_Xhi9M,422
numpy/typing/tests/data/pass/warnings_and_errors.py,sha256=ETLZkDTGpZspvwjVYAZlnA1gH4PJ4bSY5PkWyxTjusU,161
numpy/typing/tests/data/reveal/arithmetic.pyi,sha256=Oqg-yp6l9Iie7IiTrmHg2vEKj_3AAVu-REqJR-_NCJw,19762
numpy/typing/tests/data/reveal/array_api_info.pyi,sha256=B9vrUZokB3taS3TeNXf_pKmbMoh0n-dvRpBWcZPfwpc,3142
numpy/typing/tests/data/reveal/array_constructors.pyi,sha256=CK_MDzsXFECNOITU43pTkdHkIWNoQwcBzKGgi9UslBU,11004
numpy/typing/tests/data/reveal/arraypad.pyi,sha256=Q1pcU4B3eRsw5jsv-S0MsEfNUbp_4aMdO_o3n0rtA2A,776
numpy/typing/tests/data/reveal/arrayprint.pyi,sha256=soSfSAZPBOI3dvaHW5DXIUmTGDFaljbvcVXIqQjgZmk,905
numpy/typing/tests/data/reveal/arraysetops.pyi,sha256=MApdRBAeWajSdDQFVCF4yF8-WZhOJLJUmZkqBz_fmzY,4509
numpy/typing/tests/data/reveal/arrayterator.pyi,sha256=Sdl-xlxIVYSZnB9C0Mo6GwXzcJRFHz7kwQevIuWW6Zk,1097
numpy/typing/tests/data/reveal/bitwise_ops.pyi,sha256=WIwuobIYzgJhN3Wm-nYqJftxN5R1AzhMC8oV4FaFVHU,3911
numpy/typing/tests/data/reveal/char.pyi,sha256=-LyIxSayAdJDHICYq7KaMgHAnGDjPvZ4ezQzNRZ6Uds,7197
numpy/typing/tests/data/reveal/chararray.pyi,sha256=r_i5FIC-2WWu6xd8uCsEmrndwQRyfIqY3KZ9ARVOdx0,6259
numpy/typing/tests/data/reveal/comparisons.pyi,sha256=wcWh7WRI5lPOk4bMtaSWdbEeIhJG2uWNxFXTd2eFbUg,7313
numpy/typing/tests/data/reveal/constants.pyi,sha256=22VOM0_IpimXviyx98OTfNVHtFu4XsPAuN7F04q1xhA,393
numpy/typing/tests/data/reveal/ctypeslib.pyi,sha256=cj8a0Z_DQZJOlZ3BqtCjKIroJz9vQkWCaAgES2GxkRk,4814
numpy/typing/tests/data/reveal/datasource.pyi,sha256=A10C5zPFLo7ggtCJdeWSx4U15ZaXXOjE6_rM91zi120,701
numpy/typing/tests/data/reveal/dtype.pyi,sha256=eQeL3ny5S6aESd3ut3NjZAFR4my2jn-8nqIR9WH2HnQ,2874
numpy/typing/tests/data/reveal/einsumfunc.pyi,sha256=pbtSfzIWUJRkDpe2riHBlvFlNSC3CqVM-SbYtBgX9H0,2044
numpy/typing/tests/data/reveal/emath.pyi,sha256=-muNpWOv_niIn-zS3gUnFO4qBZAouNlVGue2x1L5Ris,2423
numpy/typing/tests/data/reveal/false_positives.pyi,sha256=AplTmZV7TS7nivU8vegbstMN5MdMv4U0JJdZ4IeeA5M,482
numpy/typing/tests/data/reveal/fft.pyi,sha256=ReQ9qn5frvJEy-g0RWpUGlPBntUS1cFSIu6WfPotHzE,1749
numpy/typing/tests/data/reveal/flatiter.pyi,sha256=pY8X8zjG8VpQOGf-NFGxN6eBYHhyqsKfxrHLSp-5JLo,1470
numpy/typing/tests/data/reveal/fromnumeric.pyi,sha256=DVYei9Ar5v7vG9DiwPprzBb-OYF2Pc1YJTEFHUOatcM,13265
numpy/typing/tests/data/reveal/getlimits.pyi,sha256=VK59PfKYvl8cF5joz3oUXyI9UgqSBdPIl-1uJzJ-oso,1642
numpy/typing/tests/data/reveal/histograms.pyi,sha256=wTkDU-4kSjz5ybOgb0-R7sQQO4U1_1FC0U_tM9W_PUg,1375
numpy/typing/tests/data/reveal/index_tricks.pyi,sha256=SUkSqiPhuYAA8f_XFkyF9YjQH9jmtzYDGqvfOenKyqg,3566
numpy/typing/tests/data/reveal/lib_function_base.pyi,sha256=xOTYIua7jmWPvhZ2LusgjodO9gsABPFziS6IweHGoAM,8936
numpy/typing/tests/data/reveal/lib_polynomial.pyi,sha256=gPrcYAohiyFlC3IgXqMOc-9UB5_oHL6ejG3ZK2hlEZA,5983
numpy/typing/tests/data/reveal/lib_utils.pyi,sha256=hYb7ELrXZXK9xpG4Sm_sRbwRuBS6moMJJ4V7yJJpdhk,536
numpy/typing/tests/data/reveal/lib_version.pyi,sha256=UCioUeykot8-nWL6goKxZnKZxtgB4lFEi9wdN_xyF1U,672
numpy/typing/tests/data/reveal/linalg.pyi,sha256=RzeKBHXnW1U2Q9krz5F6EZSmk_yjo29T1R-iDyeb8gw,6324
numpy/typing/tests/data/reveal/matrix.pyi,sha256=1Dl5XuO7xCRAnox5EwM-ySj9asFWx2N09Cj8eG9Q8OU,2918
numpy/typing/tests/data/reveal/memmap.pyi,sha256=A5PovMzjRp2zslF1vw3TdTQjj4Y0dIEJ__HDBV_svGM,842
numpy/typing/tests/data/reveal/mod.pyi,sha256=1MKvcgPBWEWEiAtTmQCkDcqX2haPYJx3c-6XF_PtCQU,5664
numpy/typing/tests/data/reveal/modules.pyi,sha256=0WPq7A-aqWkJsV-IA1_7dFNCcxBacj1AWExaXbXErG4,1958
numpy/typing/tests/data/reveal/multiarray.pyi,sha256=v0Xgh013jdYuBsHGg2qJRPldiLpuOkl0RLIOzWiA61U,5149
numpy/typing/tests/data/reveal/nbit_base_example.pyi,sha256=DRUMGatQvQXTuovKEMF4dzazIU6it6FU53LkOEo2vNo,657
numpy/typing/tests/data/reveal/ndarray_conversion.pyi,sha256=ovigNoNijqvE-Z1ZvGSWEwqG8Rut8JCInj_bMdoEYz4,1856
numpy/typing/tests/data/reveal/ndarray_misc.pyi,sha256=J5fq1z_SSjczJOXMFj3K0wwWPAMi-7ewQSg7ZIdNXDY,7354
numpy/typing/tests/data/reveal/ndarray_shape_manipulation.pyi,sha256=QDQ9g6l-e73pTJp-Dosiynb-okbqi91D4KirjhIjcv4,1233
numpy/typing/tests/data/reveal/nditer.pyi,sha256=VFXnT75BgWSUpb-dD-q5cZkfeOqsk-x9cH626g9FWT4,2021
numpy/typing/tests/data/reveal/nested_sequence.pyi,sha256=IQyRlXduk-ZEakOtoliMLCqNgGbeg0mzZf-a-a3Gq_0,734
numpy/typing/tests/data/reveal/npyio.pyi,sha256=CimelY4GQX-RMx6FjBz99egW3X7bZGaG0rInQXZaR6s,3611
numpy/typing/tests/data/reveal/numeric.pyi,sha256=--WlV8PubhLm151OKLk7qwuuryPQaJ1v7G8ePNZN-9Q,6152
numpy/typing/tests/data/reveal/numerictypes.pyi,sha256=TXsRnRhWFwdxckiQH_aJ1pfJbXnUfYS1OrhlixyP-2U,1390
numpy/typing/tests/data/reveal/polynomial_polybase.pyi,sha256=LMlqHMbwRSpzQT6LiqWIykKZgO59viVVmdetzEyreHs,8102
numpy/typing/tests/data/reveal/polynomial_polyutils.pyi,sha256=1R4tvQVZb6zXq7ZpiaU-7sZxBbEl9QbbCXwe5wS306g,10905
numpy/typing/tests/data/reveal/polynomial_series.pyi,sha256=CuhuEoOTcbwoBmgFjQ3Tp8dadRKlwoAw1wRTPB57lw0,7216
numpy/typing/tests/data/reveal/random.pyi,sha256=GratcSvCMhnV0oRvCrf1_5NwYrKvQFQHxUtJ3leWGAM,104393
numpy/typing/tests/data/reveal/rec.pyi,sha256=tQS7ozIsq6uJ-atFuluT_pNkgQDqYl-zfXkCwNftlcw,3853
numpy/typing/tests/data/reveal/scalars.pyi,sha256=PWo_pzZnTJKL7d3dbPLnq1d_9DEO3Y8VR-Kg7u_7kRQ,4591
numpy/typing/tests/data/reveal/shape.pyi,sha256=r0y0iSyVabz6hnIRQFdomLV6yvPqiXrGm0pVtTmm1Eg,292
numpy/typing/tests/data/reveal/shape_base.pyi,sha256=cU-ud4H33LXQ-tgmrEoOZ3ofIpER0KbDf4hOSr6-7zE,2134
numpy/typing/tests/data/reveal/stride_tricks.pyi,sha256=9-XEw94BheIL7mFuW4KdWgFCqpYxjH7I9tKYBougMEc,1433
numpy/typing/tests/data/reveal/strings.pyi,sha256=kfz7t_jZjUFvtp2ySC0H3NW8xtUcptPdltKh9Bew6zw,6373
numpy/typing/tests/data/reveal/testing.pyi,sha256=-uDO8ueb84xFgzrj1HGrLqtnhrRr5_4HCVlccBcdAZc,8610
numpy/typing/tests/data/reveal/twodim_base.pyi,sha256=0BpwrWZVzJH1M1dphlurTfk_9_IC2Y9tOnmPWAltKTw,4387
numpy/typing/tests/data/reveal/type_check.pyi,sha256=wKHbJXNosHyuV5Cmk3XdI4tAEmYgz-JklOD6P4pEblA,2756
numpy/typing/tests/data/reveal/ufunc_config.pyi,sha256=m9Cu_D8ygBCXWP_86cnfz5xHjTXFkVTxv-E_TuSQlW4,1316
numpy/typing/tests/data/reveal/ufunclike.pyi,sha256=5VhzeeEJTTY0s3RObhWIsxpuGAvpBG0BE9acvODPROU,1321
numpy/typing/tests/data/reveal/ufuncs.pyi,sha256=0WNLpLljh_EBKpHmN84Y5b5TM0rVwGf_SExGxoIHvG0,4512
numpy/typing/tests/data/reveal/warnings_and_errors.pyi,sha256=4KVv0HCz6Fu54WPq-594ruuPiPIGSiLewfRDAScNxrM,549
numpy/typing/tests/test_isfile.py,sha256=77lnjlxFqhrIRfGpSrqmvIVwpo9VoOPGiS7rRQSdKT0,865
numpy/typing/tests/test_runtime.py,sha256=2qu8JEliITnZCBJ_QJpohacj_OQ08o73ixS2w2ooNXI,3275
numpy/typing/tests/test_typing.py,sha256=A5JAOarysBiYRo5kKlANM0iYQ8xTETk0Uf-zVrl8ihE,8306
numpy/version.py,sha256=Vv6Fqb2ltfMLT851uHmE2kfj-0T064KrD5cdYrjFVCM,293
numpy/version.pyi,sha256=vD0wC6rZasZXrv6VKzpyvgMcCb0cUB8AaMEyMZvY2Yc,476
