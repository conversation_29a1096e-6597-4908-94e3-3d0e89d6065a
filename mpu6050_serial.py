"""
نسخه سریال MPU6050 برای ESP32
خروجی استاندارد برای خواندن از طریق Python Serial
فرمت: JSON ساده برای پردازش آسان
"""

import time
import gc
import ujson
from machine import I2C, Pin

# وارد کردن ماژول‌ها
try:
    from config_loader import config_loader
    from sensor_filters import SensorDataFilter
except ImportError:
    print("ERROR: فایل‌های config_loader.py یا sensor_filters.py یافت نشدند")
    # تنظیمات پیش‌فرض
    class MockConfig:
        def get(self, section, key, default=None):
            configs = {
                ("hardware", "i2c_scl_pin"): 22,
                ("hardware", "i2c_sda_pin"): 21,
                ("hardware", "i2c_frequency"): 400000,
                ("hardware", "mpu6050_address"): 0x68,
                ("sensor", "accel_scale"): 16384.0,
                ("sensor", "gyro_scale"): 131.0,
                ("timing", "read_interval_ms"): 500,
                ("serial", "output_format"): "json",
                ("serial", "include_debug"): False
            }
            return configs.get((section, key), default)
    
    config_loader = MockConfig()
    
    # فیلتر ساده
    class MockFilter:
        def __init__(self, config): pass
        def filter_data(self, data): return data
        def reset_filters(self): pass
    
    SensorDataFilter = MockFilter

class MPU6050Serial:
    """کلاس MPU6050 بهینه شده برای خروجی سریال"""
    
    # آدرس‌های رجیستر
    PWR_MGMT_1 = 0x6B
    ACCEL_XOUT_H = 0x3B
    
    def __init__(self):
        """مقداردهی اولیه سنسور"""
        self.sample_count = 0
        
        # تنظیمات از config
        self.accel_scale = config_loader.get("sensor", "accel_scale", 16384.0)
        self.gyro_scale = config_loader.get("sensor", "gyro_scale", 131.0)
        self.address = config_loader.get("hardware", "mpu6050_address", 0x68)
        
        # تنظیمات سریال
        self.output_format = config_loader.get("serial", "output_format", "json")
        self.include_debug = config_loader.get("serial", "include_debug", False)
        
        # مقداردهی I2C
        self._init_i2c()
        
        # مقداردهی سنسور
        self._init_sensor()
        
        # فیلتر داده‌ها
        self.data_filter = SensorDataFilter(config_loader)
    
    def _init_i2c(self):
        """مقداردهی I2C"""
        try:
            scl_pin = config_loader.get("hardware", "i2c_scl_pin", 22)
            sda_pin = config_loader.get("hardware", "i2c_sda_pin", 21)
            frequency = config_loader.get("hardware", "i2c_frequency", 400000)
            
            self.i2c = I2C(scl=Pin(scl_pin), sda=Pin(sda_pin), freq=frequency)
            
            if self.include_debug:
                print(f"DEBUG: I2C initialized - SCL:{scl_pin}, SDA:{sda_pin}, FREQ:{frequency}")
        except Exception as e:
            print(f"ERROR: I2C init failed - {e}")
            raise
    
    def _init_sensor(self):
        """مقداردهی سنسور MPU6050"""
        try:
            # بیدار کردن سنسور
            self.i2c.writeto_mem(self.address, self.PWR_MGMT_1, b'\x00')
            time.sleep_ms(100)
            
            if self.include_debug:
                print(f"DEBUG: MPU6050 initialized at address 0x{self.address:02X}")
        except Exception as e:
            print(f"ERROR: Sensor init failed - {e}")
            raise
    
    def _bytes_to_int(self, high_byte, low_byte):
        """تبدیل دو بایت به عدد صحیح با علامت"""
        value = (high_byte << 8) | low_byte
        return value - 65536 if value > 32767 else value
    
    def read_sensor_data(self):
        """
        خواندن داده‌های سنسور
        
        Returns:
            dict: داده‌های سنسور یا None در صورت خطا
        """
        try:
            # خواندن 14 بایت داده
            raw_data = self.i2c.readfrom_mem(self.address, self.ACCEL_XOUT_H, 14)
            
            self.sample_count += 1
            
            # تبدیل داده‌های خام
            accel_x = self._bytes_to_int(raw_data[0], raw_data[1]) / self.accel_scale
            accel_y = self._bytes_to_int(raw_data[2], raw_data[3]) / self.accel_scale
            accel_z = self._bytes_to_int(raw_data[4], raw_data[5]) / self.accel_scale
            
            gyro_x = self._bytes_to_int(raw_data[8], raw_data[9]) / self.gyro_scale
            gyro_y = self._bytes_to_int(raw_data[10], raw_data[11]) / self.gyro_scale
            gyro_z = self._bytes_to_int(raw_data[12], raw_data[13]) / self.gyro_scale
            
            temp_raw = self._bytes_to_int(raw_data[6], raw_data[7])
            temperature = temp_raw / 340.0 + 36.53
            
            # ایجاد دیکشنری داده‌ها
            sensor_data = {
                'id': self.sample_count,
                'time': time.ticks_ms(),
                'ax': round(accel_x, 4),
                'ay': round(accel_y, 4),
                'az': round(accel_z, 4),
                'gx': round(gyro_x, 4),
                'gy': round(gyro_y, 4),
                'gz': round(gyro_z, 4),
                'temp': round(temperature, 2)
            }
            
            # اعمال فیلتر
            filtered_data = self.data_filter.filter_data({
                'sample_id': sensor_data['id'],
                'timestamp': sensor_data['time'],
                'accel_x': sensor_data['ax'],
                'accel_y': sensor_data['ay'],
                'accel_z': sensor_data['az'],
                'gyro_x': sensor_data['gx'],
                'gyro_y': sensor_data['gy'],
                'gyro_z': sensor_data['gz'],
                'temperature': sensor_data['temp']
            })
            
            # اضافه کردن زوایا در صورت وجود
            if 'angle_x' in filtered_data:
                sensor_data['rx'] = round(filtered_data['angle_x'], 2)  # Roll
                sensor_data['ry'] = round(filtered_data['angle_y'], 2)  # Pitch  
                sensor_data['rz'] = round(filtered_data['angle_z'], 2)  # Yaw
            
            return sensor_data
            
        except Exception as e:
            if self.include_debug:
                print(f"ERROR: Read failed - {e}")
            return None
    
    def print_data_json(self, data):
        """چاپ داده‌ها در فرمت JSON"""
        if data:
            try:
                json_str = ujson.dumps(data)
                print(f"DATA:{json_str}")
            except Exception as e:
                if self.include_debug:
                    print(f"ERROR: JSON encode failed - {e}")
    
    def print_data_csv(self, data):
        """چاپ داده‌ها در فرمت CSV"""
        if data:
            try:
                # فرمت: id,time,ax,ay,az,gx,gy,gz,rx,ry,rz,temp
                csv_line = f"DATA:{data['id']},{data['time']},{data['ax']},{data['ay']},{data['az']},"
                csv_line += f"{data['gx']},{data['gy']},{data['gz']},"
                csv_line += f"{data.get('rx', 0)},{data.get('ry', 0)},{data.get('rz', 0)},{data['temp']}"
                print(csv_line)
            except Exception as e:
                if self.include_debug:
                    print(f"ERROR: CSV format failed - {e}")
    
    def run_continuous(self):
        """اجرای مداوم سنسور"""
        interval_ms = config_loader.get("timing", "read_interval_ms", 500)
        gc_threshold = config_loader.get("memory", "gc_threshold", 20)
        
        if self.include_debug:
            print(f"DEBUG: Starting continuous mode - interval:{interval_ms}ms")
        
        # چاپ header برای CSV
        if self.output_format == "csv":
            print("HEADER:id,time,ax,ay,az,gx,gy,gz,rx,ry,rz,temp")
        
        print("READY")  # سیگنال آماده بودن
        
        try:
            while True:
                start_time = time.ticks_ms()
                
                # خواندن داده‌ها
                data = self.read_sensor_data()
                
                # چاپ داده‌ها
                if data:
                    if self.output_format == "json":
                        self.print_data_json(data)
                    else:
                        self.print_data_csv(data)
                
                # مدیریت حافظه
                if self.sample_count % gc_threshold == 0:
                    gc.collect()
                
                # مدیریت زمان‌بندی
                elapsed = time.ticks_diff(time.ticks_ms(), start_time)
                if elapsed < interval_ms:
                    time.sleep_ms(interval_ms - elapsed)
        
        except KeyboardInterrupt:
            print("STOPPED")
        except Exception as e:
            print(f"ERROR: Runtime error - {e}")

def main():
    """تابع اصلی"""
    try:
        # ایجاد سنسور
        mpu = MPU6050Serial()
        
        # شروع خواندن مداوم
        mpu.run_continuous()
        
    except Exception as e:
        print(f"FATAL: {e}")

if __name__ == "__main__":
    main()
