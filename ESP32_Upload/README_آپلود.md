# 📤 فایل‌های آپلود ESP32

این پوشه شامل **همه فایل‌های ضروری** برای آپلود روی ESP32 است.

## 📁 فایل‌های موجود:

```
ESP32_Upload/
├── 📋 فایل‌های ضروری ESP32:
│   ├── config.json              # تنظیمات سیستم (JSON ایمن)
│   ├── config_loader.py         # بارگذار تنظیمات
│   ├── sensor_filters.py        # فیلترهای پیشرفته نویز
│   ├── mpu6050_enhanced.py      # کلاس سنسور پیشرفته
│   ├── mpu6050_serial.py        # نسخه سریال بهینه
│   ├── dataset_creator.py       # ایجادکننده دیتاست
│   └── main.py                  # فایل شروع خودکار
│
├── 🚀 اسکریپت‌های آپلود:
│   ├── upload_script.py         # اسکریپت Python خودکار
│   ├── upload_windows.bat       # اسکریپت Windows
│   └── upload_linux.sh          # اسکریپت Linux/Mac
│
└── 📖 راهنما:
    └── README_آپلود.md         # این راهنما
```

## 🚀 نحوه آپلود:

### 🎯 روش 1: اسکریپت خودکار (پیشنهادی)

**Windows:**
```cmd
upload_windows.bat
```

**Linux/Mac:**
```bash
./upload_linux.sh
```

**Python (همه سیستم‌ها):**
```bash
python upload_script.py --port COM3        # Windows
python upload_script.py --port /dev/ttyUSB0  # Linux
```

### 🔧 روش 2: Thonny IDE
1. باز کردن Thonny
2. اتصال به ESP32
3. انتخاب 7 فایل ضروری (بجز README و اسکریپت‌ها)
4. کلیک راست → "Upload to /"

### ⚙️ روش 3: دستی با ampy
```bash
cd ESP32_Upload
ampy --port COM3 put config.json
ampy --port COM3 put config_loader.py
ampy --port COM3 put sensor_filters.py
ampy --port COM3 put mpu6050_enhanced.py
ampy --port COM3 put mpu6050_serial.py
ampy --port COM3 put dataset_creator.py
ampy --port COM3 put main.py
```

## ⚡ ترتیب آپلود پیشنهادی:

1. **config.json** (تنظیمات)
2. **config_loader.py** (بارگذار)
3. **sensor_filters.py** (فیلترها)
4. **mpu6050_enhanced.py** (سنسور پیشرفته)
5. **mpu6050_serial.py** (نسخه سریال)
6. **dataset_creator.py** (دیتاست)
7. **main.py** (شروع خودکار)

## 📊 اطلاعات فایل‌ها:

- **تعداد کل**: 7 فایل
- **حجم کل**: ~55.7 KB
- **سازگاری**: MicroPython ESP32
- **وابستگی**: بدون وابستگی خارجی

## 🔧 بعد از آپلود:

1. **Reset کردن ESP32**
2. **اتصال سریال** (115200 baud)
3. **اجرای خواننده سریال**:
   ```bash
   python serial_reader.py --port COM3 --duration 60
   ```

## ✅ تست صحت آپلود:

در REPL ESP32:
```python
import os
print(os.listdir())
# باید همه 7 فایل را نشان دهد

from mpu6050_serial import MPU6050Serial
mpu = MPU6050Serial()
print("✅ آپلود موفق!")
```

## 🎯 نتیجه:

بعد از آپلود موفق، ESP32 شما دارای:
- **📊 داده‌های شتاب** در 3 محور
- **🔄 داده‌های ژایروسکوپ** در 3 محور  
- **📐 زوایای محاسبه شده** (Roll, Pitch, Yaw)
- **🔧 فیلترهای پیشرفته نویز**
- **📡 خروجی سریال استاندارد**

---
**نکته**: فایل README_آپلود.md را آپلود نکنید - فقط برای راهنمایی است!
