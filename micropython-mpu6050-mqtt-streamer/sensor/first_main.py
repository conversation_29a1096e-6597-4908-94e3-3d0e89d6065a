
import network
import time
import json
import urandom
from machine import I2C, Pin
import mpu6050
from umqtt.simple import MQTTClient

# --- Configuration ---
# Please create a config.py file with your WiFi credentials
# Example config.py:
# WIFI_SSID = "YourWiFiSSID"
# WIFI_PASSWORD = "YourWiFiPassword"
try:
    import config
except ImportError:
    print("Error: config.py not found. Please create it with your WiFi credentials.")
    # Use placeholders if config.py is not found
    class config:
        WIFI_SSID = "YOUR_WIFI_SSID"
        WIFI_PASSWORD = "YOUR_WIFI_PASSWORD"


# I2C and MQTT Configuration
I2C_SCL_PIN = 22
I2C_SDA_PIN = 21
MQTT_BROKER = "broker.mqttdashboard.com"
MQTT_TOPIC = b"micropython/test/mpu6050"
CLIENT_ID = str(urandom.getrandbits(32))
LOOP_DELAY_MS = 20


def connect_wifi(ssid, password):
    """
    Connects the device to a Wi-Fi network.

    Args:
        ssid (str): The SSID of the Wi-Fi network.
        password (str): The password for the Wi-Fi network.
    """
    station = network.WLAN(network.STA_IF)
    station.active(True)
    if not station.isconnected():
        print(f"Connecting to network '{ssid}'...")
        station.connect(ssid, password)
        while not station.isconnected():
            time.sleep(1)
    print("Network connection successful")
    print(station.ifconfig())


def run_sensor_stream():
    """
    Initializes the sensor and MQTT client, then enters a loop
    to read sensor data and publish it.
    """
    # Initialize I2C communication with the MPU6050 sensor
    print("Initializing I2C and MPU6050 sensor...")
    i2c = I2C(scl=Pin(I2C_SCL_PIN), sda=Pin(I2C_SDA_PIN))
    mpu = mpu6050.accel(i2c)
    print("Sensor initialized.")

    # Initialize and connect the MQTT client
    print(f"Connecting to MQTT broker at {MQTT_BROKER}...")
    mqtt_client = MQTTClient(CLIENT_ID, MQTT_BROKER)
    mqtt_client.connect()
    print("MQTT client connected.")

    # Main loop to read and publish data
    print("Starting to stream sensor data...")
    while True:
        try:
            # Get sensor values from the MPU6050
            values = mpu.get_values()

            # Publish the sensor values to the MQTT topic as a JSON string
            mqtt_client.publish(MQTT_TOPIC, json.dumps(values))

            # Print values to the console for local debugging
            print(values)

            # Wait for the specified delay before the next reading
            time.sleep_ms(LOOP_DELAY_MS)

        except OSError as e:
            print(f"An error occurred: {e}")
            # In case of an error (e.g., I2C issue), wait before retrying
            time.sleep(5)


def main():
    """
    Main function to set up the device and start the sensor stream.
    """
    # A small delay at the start can help with stability on some boards
    time.sleep_ms(1000)

    # Connect to the WiFi network
    connect_wifi(config.WIFI_SSID, config.WIFI_PASSWORD)

    # Start the main process of streaming sensor data
    run_sensor_stream()

