# کتابخانه‌های مورد نیاز برای پروژه MPU6050 CSV Logger

# برای ESP32 + MicroPython:
# این کتابخانه‌ها به صورت پیش‌فرض در MicroPython موجود هستند:
# - machine (برای I2C و Pin)
# - time (برای مدیریت زمان)
# - gc (برای مدیریت حافظه)
# - os (برای عملیات فایل)

# برای تست روی کامپیوتر (Python استاندارد):
# هیچ کتابخانه اضافی مورد نیاز نیست

# ابزارهای توسعه (اختیاری):
# esptool>=4.0  # برای فلش کردن ESP32
# ampy>=1.1.0   # برای انتقال فایل به ESP32
# thonny>=4.0   # IDE برای MicroPython

# نکته: برای نصب MicroPython روی ESP32:
# pip install esptool
# esptool.py --chip esp32 erase_flash
# esptool.py --chip esp32 write_flash -z 0x1000 esp32-*.bin
