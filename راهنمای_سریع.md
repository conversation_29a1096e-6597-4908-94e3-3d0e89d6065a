# راهنمای سریع - سیستم لاگ MPU6050

## 📋 خلاصه پروژه

این پروژه یک سیستم کامل برای خواندن داده‌های سنسور MPU6050 و ذخیره آن‌ها در فایل CSV است که با ESP32 و MicroPython کار می‌کند.

## 🎯 ویژگی‌های کلیدی

- ✅ خواندن 6 محور داده (3 شتاب + 3 زاویه) هر نیم ثانیه
- ✅ ذخیره بهینه در فایل CSV
- ✅ مدیریت حافظه و خطا
- ✅ قابل تنظیم و بهینه‌شده

## 🔌 اتصالات سخت‌افزاری

```
ESP32    →    MPU6050
3.3V     →    VCC
GND      →    GND  
GPIO21   →    SDA
GPIO22   →    SCL
```

## 📁 فایل‌های پروژه

| فایل | توضیح |
|------|-------|
| `mpu6050_csv_logger.py` | کد اصلی سیستم |
| `config.py` | تنظیمات قابل تغییر |
| `boot.py` | راه‌اندازی خودکار ESP32 |
| `main.py` | شروع خودکار لاگ |
| `test_mpu6050.py` | تست سیستم |
| `README.md` | راهنمای کامل |

## 🚀 راه‌اندازی سریع

### 1. آماده‌سازی ESP32
```bash
# نصب ابزار فلش
pip install esptool

# پاک کردن فلش
esptool.py --chip esp32 --port /dev/ttyUSB0 erase_flash

# نصب MicroPython
esptool.py --chip esp32 --port /dev/ttyUSB0 write_flash -z 0x1000 esp32-*.bin
```

### 2. کپی فایل‌ها
فایل‌های زیر را روی ESP32 کپی کنید:
- `mpu6050_csv_logger.py`
- `config.py`
- `boot.py` (اختیاری)
- `main.py` (اختیاری)

### 3. اجرا

#### روش 1: دستی
```python
from mpu6050_csv_logger import main
main()
```

#### روش 2: خودکار
فقط ESP32 را روشن کنید (اگر main.py کپی کرده‌اید)

## ⚙️ تنظیمات مهم

در فایل `config.py`:

```python
# فاصله زمانی خواندن (میلی‌ثانیه)
READ_INTERVAL_MS = 500  # نیم ثانیه

# اندازه بافر (تعداد نمونه قبل از ذخیره)
BUFFER_SIZE = 5

# نام فایل خروجی
CSV_FILENAME = "mpu6050_data.csv"

# نمایش در کنسول
SHOW_CONSOLE_OUTPUT = True
```

## 📊 فرمت خروجی CSV

```csv
sample_id,timestamp,accel_x,accel_y,accel_z,gyro_x,gyro_y,gyro_z,temperature
1,12345,0.123,-0.456,0.987,12.34,-56.78,98.76,25.4
2,12845,0.234,-0.567,1.098,23.45,-67.89,109.87,25.5
```

## 🔧 عیب‌یابی

### مشکلات رایج:

1. **خطای I2C**: بررسی اتصالات سیم‌کشی
2. **خطای حافظه**: کاهش `BUFFER_SIZE` در config.py
3. **خطای فایل**: بررسی فضای ذخیره‌سازی ESP32

### تست سیستم:
```python
# اجرای تست روی کامپیوتر
python test_mpu6050.py
```

## 📈 نمونه خروجی

```
==================================================
سیستم لاگ داده‌های MPU6050
==================================================
فایل خروجی: mpu6050_data.csv
فاصله زمانی خواندن: 500 میلی‌ثانیه
اندازه بافر: 5 رکورد
--------------------------------------------------
سنسور MPU6050 در آدرس 0x68 مقداردهی شد
شروع جمع‌آوری داده‌ها...
--------------------------------------------------
نمونه 0001 | شتاب: (+0.123, -0.456, +0.987) g | زاویه: (+12.34, -56.78, +98.76) °/s
نمونه 0002 | شتاب: (+0.234, -0.567, +1.098) g | زاویه: (+23.45, -67.89, +109.87) °/s
```

## 💡 نکات مهم

- برای توقف: `Ctrl+C`
- داده‌ها هر 5 نمونه ذخیره می‌شوند
- حافظه به طور خودکار مدیریت می‌شود
- فایل CSV قابل باز کردن در Excel است

## 📞 پشتیبانی

برای سوالات و مشکلات:
- بررسی فایل `README.md` برای جزئیات بیشتر
- اجرای `test_mpu6050.py` برای تست سیستم
- بررسی اتصالات سخت‌افزاری

---
**موفق باشید! 🎉**
