"""DO NOT EDIT.

This file was autogenerated. Do not edit it by hand,
since your modifications would be overwritten.
"""

from keras.src.legacy.losses import Reduction as Reduction
from keras.src.losses import deserialize as deserialize
from keras.src.losses import get as get
from keras.src.losses import serialize as serialize
from keras.src.losses.loss import Loss as Loss
from keras.src.losses.losses import CTC as CTC
from keras.src.losses.losses import BinaryCrossentropy as BinaryCrossentropy
from keras.src.losses.losses import (
    BinaryFocalCrossentropy as BinaryFocalCrossentropy,
)
from keras.src.losses.losses import (
    CategoricalCrossentropy as CategoricalCrossentropy,
)
from keras.src.losses.losses import (
    CategoricalFocalCrossentropy as CategoricalFocalCrossentropy,
)
from keras.src.losses.losses import (
    CategoricalGeneralizedCrossEntropy as CategoricalGeneralizedCrossEntropy,
)
from keras.src.losses.losses import CategoricalHinge as CategoricalHinge
from keras.src.losses.losses import Circle as Circle
from keras.src.losses.losses import CosineSimilarity as CosineSimilarity
from keras.src.losses.losses import Dice as Dice
from keras.src.losses.losses import Hinge as Hinge
from keras.src.losses.losses import Huber as Huber
from keras.src.losses.losses import KLDivergence as KLDivergence
from keras.src.losses.losses import LogCosh as LogCosh
from keras.src.losses.losses import MeanAbsoluteError as MeanAbsoluteError
from keras.src.losses.losses import (
    MeanAbsolutePercentageError as MeanAbsolutePercentageError,
)
from keras.src.losses.losses import MeanSquaredError as MeanSquaredError
from keras.src.losses.losses import (
    MeanSquaredLogarithmicError as MeanSquaredLogarithmicError,
)
from keras.src.losses.losses import Poisson as Poisson
from keras.src.losses.losses import (
    SparseCategoricalCrossentropy as SparseCategoricalCrossentropy,
)
from keras.src.losses.losses import SquaredHinge as SquaredHinge
from keras.src.losses.losses import Tversky as Tversky
from keras.src.losses.losses import binary_crossentropy as binary_crossentropy
from keras.src.losses.losses import (
    binary_focal_crossentropy as binary_focal_crossentropy,
)
from keras.src.losses.losses import (
    categorical_crossentropy as categorical_crossentropy,
)
from keras.src.losses.losses import (
    categorical_focal_crossentropy as categorical_focal_crossentropy,
)
from keras.src.losses.losses import (
    categorical_generalized_cross_entropy as categorical_generalized_cross_entropy,
)
from keras.src.losses.losses import categorical_hinge as categorical_hinge
from keras.src.losses.losses import circle as circle
from keras.src.losses.losses import cosine_similarity as cosine_similarity
from keras.src.losses.losses import ctc as ctc
from keras.src.losses.losses import dice as dice
from keras.src.losses.losses import hinge as hinge
from keras.src.losses.losses import huber as huber
from keras.src.losses.losses import kl_divergence as KLD
from keras.src.losses.losses import kl_divergence as kld
from keras.src.losses.losses import kl_divergence as kullback_leibler_divergence
from keras.src.losses.losses import log_cosh as logcosh
from keras.src.losses.losses import mean_absolute_error as MAE
from keras.src.losses.losses import mean_absolute_error as mae
from keras.src.losses.losses import mean_absolute_percentage_error as MAPE
from keras.src.losses.losses import mean_absolute_percentage_error as mape
from keras.src.losses.losses import mean_squared_error as MSE
from keras.src.losses.losses import mean_squared_error as mse
from keras.src.losses.losses import mean_squared_logarithmic_error as MSLE
from keras.src.losses.losses import mean_squared_logarithmic_error as msle
from keras.src.losses.losses import poisson as poisson
from keras.src.losses.losses import (
    sparse_categorical_crossentropy as sparse_categorical_crossentropy,
)
from keras.src.losses.losses import squared_hinge as squared_hinge
from keras.src.losses.losses import tversky as tversky
