"""
فایل تنظیمات برای پروژه MPU6050 CSV Logger
"""

# تنظیمات پین‌های I2C
I2C_SCL_PIN = 22  # پین کلاک I2C
I2C_SDA_PIN = 21  # پین داده I2C
I2C_FREQUENCY = 400000  # فرکانس I2C (400 کیلوهرتز)

# تنظیمات سنسور MPU6050
MPU6050_ADDRESS = 0x68  # آدرس پیش‌فرض سنسور

# تنظیمات لاگ داده‌ها
CSV_FILENAME = "mpu6050_data.csv"  # نام فایل CSV
BUFFER_SIZE = 5  # تعداد رکوردهای بافر قبل از نوشتن
READ_INTERVAL_MS = 500  # فاصله زمانی خواندن (میلی‌ثانیه)

# تنظیمات نمایش
SHOW_CONSOLE_OUTPUT = True  # نمایش داده‌ها در کنسول
DECIMAL_PLACES = 4  # تعداد رقم اعشار

# تنظیمات بهینه‌سازی
ENABLE_GARBAGE_COLLECTION = True  # فعال‌سازی جمع‌آوری زباله
GC_THRESHOLD = 10  # آستانه جمع‌آوری زباله (تعداد رکورد)
