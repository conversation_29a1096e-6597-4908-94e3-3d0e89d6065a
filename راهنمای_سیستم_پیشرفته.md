# 🚀 راهنمای سیستم پیشرفته MPU6050

## 📋 خلاصه سیستم

سیستم پیشرفته MPU6050 برای ESP32 با MicroPython که شامل:

### ✨ ویژگی‌های اصلی:
- 📊 **داده‌های شتاب** در 3 محور (X, Y, Z) - واحد: g
- 🔄 **داده‌های ژایروسکوپ** در 3 محور (X, Y, Z) - واحد: °/s  
- 📐 **زوایای محاسبه شده** (Roll, Pitch, Yaw) - واحد: درجه
- 🔧 **فیلترهای پیشرفته نویز** (Low-pass, Moving Average, Threshold)
- ⏱️  **تایمینگ دقیق**: 6 نمونه در هر 3 ثانیه (هر 0.5 ثانیه)
- 💾 **بهینه‌سازی حافظه** و مدیریت خطای پیشرفته

## 📁 ساختار فایل‌ها

```
📁 فایل‌های اصلی:
├── config.json              # تنظیمات سیستم (JSON ایمن)
├── config_loader.py         # بارگذار تنظیمات
├── sensor_filters.py        # فیلترهای پیشرفته نویز
├── mpu6050_enhanced.py      # کلاس اصلی سنسور پیشرفته
└── dataset_creator.py       # ایجادکننده دیتاست

📁 فایل‌های تست:
├── test_enhanced_system.py  # تست کامل سیستم
└── test_dataset_creator.py  # تست ایجاد دیتاست

📁 فایل‌های قدیمی (سازگاری):
├── mpu6050_csv_logger.py    # نسخه قدیمی
├── config.py                # تنظیمات قدیمی (منسوخ)
├── boot.py                  # راه‌اندازی خودکار
└── main.py                  # شروع خودکار
```

## ⚙️ تنظیمات سیستم (config.json)

```json
{
  "hardware": {
    "i2c_scl_pin": 22,
    "i2c_sda_pin": 21,
    "i2c_frequency": 400000,
    "mpu6050_address": 104
  },
  "timing": {
    "read_interval_ms": 500
  },
  "sensor": {
    "accel_scale": 16384.0,
    "gyro_scale": 131.0,
    "enable_filtering": true,
    "filter_type": "low_pass",
    "filter_alpha": 0.8,
    "noise_threshold": 0.01
  },
  "dataset": {
    "samples_per_collection": 6,
    "default_samples": 10,
    "default_rest_duration": 2
  }
}
```

## 🔧 انواع فیلترهای نویز

### 1. فیلتر پایین‌گذر (Low-Pass Filter)
- **کاربرد**: حذف نویزهای فرکانس بالا
- **پارامتر**: `alpha` (0-1) - مقادیر بالاتر = فیلتر کمتر
- **فرمول**: `y[n] = α × x[n] + (1-α) × y[n-1]`

### 2. فیلتر میانگین متحرک (Moving Average)
- **کاربرد**: هموارسازی داده‌ها
- **پارامتر**: `window_size` - اندازه پنجره میانگین
- **مزیت**: پاسخ سریع به تغییرات

### 3. فیلتر آستانه نویز (Noise Threshold)
- **کاربرد**: حذف مقادیر کوچک (نویز)
- **پارامتر**: `threshold` - آستانه حذف
- **عملکرد**: مقادیر کمتر از آستانه = صفر

### 4. فیلتر مکمل (Complementary Filter)
- **کاربرد**: ترکیب داده‌های شتاب و ژایرو برای محاسبه زاویه
- **پارامتر**: `alpha` (معمولاً 0.98)
- **خروجی**: زوایای فیلتر شده (Roll, Pitch, Yaw)

## 📊 فرمت داده‌های خروجی

### CSV Header:
```
sample_id,timestamp,accel_x,accel_y,accel_z,gyro_x,gyro_y,gyro_z,angle_x,angle_y,angle_z,temperature
```

### نمونه داده:
```
1,1753744230579,0.4509,0.6106,-0.642,-234.0768,-91.1612,-15.6093,45.2,-12.8,180.5,25.01
```

### توضیح ستون‌ها:
- `sample_id`: شماره نمونه
- `timestamp`: زمان (میلی‌ثانیه)
- `accel_x/y/z`: شتاب در محورهای X, Y, Z (g)
- `gyro_x/y/z`: سرعت زاویه‌ای در محورهای X, Y, Z (°/s)
- `angle_x/y/z`: زوایای محاسبه شده Roll, Pitch, Yaw (درجه)
- `temperature`: دمای سنسور (°C)

## 🚀 نحوه استفاده

### 1. اجرای مستقیم سنسور:
```python
from mpu6050_enhanced import main_enhanced
main_enhanced()
```

### 2. ایجاد دیتاست:
```python
from dataset_creator import DatasetCreator

# ایجاد 10 نمونه با فاصله 2 ثانیه
creator = DatasetCreator("my_dataset")
files = creator.create_dataset(num_samples=10, rest_duration=2)
```

### 3. تست سیستم:
```bash
python test_enhanced_system.py
```

## 🔍 مثال کد کامل

```python
from mpu6050_enhanced import MPU6050Enhanced, EnhancedCSVLogger
from machine import I2C, Pin
import time

# مقداردهی I2C
i2c = I2C(scl=Pin(22), sda=Pin(21), freq=400000)

# ایجاد سنسور پیشرفته
mpu = MPU6050Enhanced(i2c)

# ایجاد لاگر
logger = EnhancedCSVLogger("data.csv")

# خواندن 10 نمونه
for i in range(10):
    data = mpu.get_sensor_data()
    if data:
        logger.add_data(data)
        print(f"نمونه {i+1}: شتاب=({data['accel_x']:.2f}, {data['accel_y']:.2f}, {data['accel_z']:.2f})")
        print(f"         زاویه=({data['angle_x']:.1f}°, {data['angle_y']:.1f}°, {data['angle_z']:.1f}°)")
    
    time.sleep_ms(500)

# ذخیره نهایی
logger.flush_buffer()
```

## ⚡ بهینه‌سازی‌ها

### 1. مدیریت حافظه:
- Garbage Collection خودکار
- بافرینگ هوشمند
- حذف متغیرهای غیرضروری

### 2. عملکرد:
- خواندن یکجای 14 بایت از سنسور
- محاسبات بهینه شده
- کش کردن تنظیمات

### 3. قابلیت اطمینان:
- مدیریت خطای پیشرفته
- بازیابی خودکار از خطاها
- تایید صحت داده‌ها

## 🛠️ عیب‌یابی

### مشکلات رایج:

1. **سنسور پیدا نمی‌شود**:
   - بررسی اتصالات I2C
   - تست آدرس سنسور (0x68 یا 0x69)

2. **داده‌های نامعقول**:
   - کالیبراسیون سنسور
   - بررسی تنظیمات فیلتر

3. **خطای حافظه**:
   - کاهش buffer_size
   - فعال کردن garbage collection

### کدهای تشخیص:
```python
# تست اتصال I2C
i2c.scan()  # باید [104] یا [0x68] برگرداند

# تست خواندن خام
raw = mpu.read_raw_data()
print(f"داده خام: {len(raw) if raw else 0} بایت")

# بازنشانی فیلترها
mpu.reset_filters()
```

## 📈 نتایج تست

✅ **تست موفقیت‌آمیز**:
- 6 داده در هر 3 ثانیه ✓
- 12 ستون داده (شامل زوایا) ✓
- فیلترهای نویز فعال ✓
- مدیریت حافظه بهینه ✓
- سازگاری با MicroPython و Python ✓

## 🎯 نکات مهم

1. **تایمینگ دقیق**: سیستم دقیقاً 6 نمونه در 3 ثانیه جمع‌آوری می‌کند
2. **فیلترهای هوشمند**: نویز کاهش می‌یابد اما داده‌های مفید حفظ می‌شوند
3. **زوایای محاسبه شده**: ترکیب بهینه شتاب و ژایرو برای زوایای دقیق
4. **تنظیمات JSON**: ایمن و قابل ویرایش بدون خطر خرابی کد
5. **سازگاری**: کار با هر دو حالت MicroPython و Python استاندارد

---
**نویسنده**: توسعه‌دهنده پارسی  
**تاریخ**: ۱۴۰۳/۱۱/۰۹  
**نسخه**: 2.0 (پیشرفته)
