"""
فیلترهای سنسور برای کاهش نویز
این ماژول شامل انواع فیلترها برای بهبود کیفیت داده‌های سنسور است
"""

import math

class LowPassFilter:
    """فیلتر پایین‌گذر برای کاهش نویز"""
    
    def __init__(self, alpha=0.8):
        """
        مقداردهی اولیه فیلتر پایین‌گذر
        
        Args:
            alpha (float): ضریب فیلتر (0-1) - مقادیر بالاتر = فیلتر کمتر
        """
        self.alpha = alpha
        self.previous_value = None
    
    def filter(self, new_value):
        """
        اعمال فیلتر پایین‌گذر
        
        Args:
            new_value (float): مقدار جدید
            
        Returns:
            float: مقدار فیلتر شده
        """
        if self.previous_value is None:
            self.previous_value = new_value
            return new_value
        
        # فرمول فیلتر پایین‌گذر: y[n] = α * x[n] + (1-α) * y[n-1]
        filtered_value = self.alpha * new_value + (1 - self.alpha) * self.previous_value
        self.previous_value = filtered_value
        return filtered_value
    
    def reset(self):
        """بازنشانی فیلتر"""
        self.previous_value = None

class MovingAverageFilter:
    """فیلتر میانگین متحرک"""
    
    def __init__(self, window_size=5):
        """
        مقداردهی اولیه فیلتر میانگین متحرک
        
        Args:
            window_size (int): اندازه پنجره میانگین
        """
        self.window_size = window_size
        self.values = []
    
    def filter(self, new_value):
        """
        اعمال فیلتر میانگین متحرک
        
        Args:
            new_value (float): مقدار جدید
            
        Returns:
            float: مقدار فیلتر شده
        """
        self.values.append(new_value)
        
        # حفظ اندازه پنجره
        if len(self.values) > self.window_size:
            self.values.pop(0)
        
        # محاسبه میانگین
        return sum(self.values) / len(self.values)
    
    def reset(self):
        """بازنشانی فیلتر"""
        self.values.clear()

class NoiseThresholdFilter:
    """فیلتر آستانه نویز - مقادیر کوچک را صفر می‌کند"""
    
    def __init__(self, threshold=0.01):
        """
        مقداردهی اولیه فیلتر آستانه نویز
        
        Args:
            threshold (float): آستانه نویز
        """
        self.threshold = threshold
    
    def filter(self, value):
        """
        اعمال فیلتر آستانه نویز
        
        Args:
            value (float): مقدار ورودی
            
        Returns:
            float: مقدار فیلتر شده
        """
        return 0.0 if abs(value) < self.threshold else value

class ComplementaryFilter:
    """فیلتر مکمل برای ترکیب داده‌های شتاب و ژایرو"""
    
    def __init__(self, alpha=0.98, dt=0.01):
        """
        مقداردهی اولیه فیلتر مکمل
        
        Args:
            alpha (float): ضریب ترکیب (0-1)
            dt (float): گام زمانی (ثانیه)
        """
        self.alpha = alpha
        self.dt = dt
        self.angle_x = 0.0
        self.angle_y = 0.0
        self.angle_z = 0.0
    
    def update(self, accel_x, accel_y, accel_z, gyro_x, gyro_y, gyro_z):
        """
        به‌روزرسانی زوایا با استفاده از فیلتر مکمل
        
        Args:
            accel_x, accel_y, accel_z (float): داده‌های شتاب‌سنج
            gyro_x, gyro_y, gyro_z (float): داده‌های ژایروسکوپ
            
        Returns:
            tuple: زوایای فیلتر شده (angle_x, angle_y, angle_z)
        """
        # محاسبه زاویه از شتاب‌سنج
        accel_angle_x = math.atan2(accel_y, math.sqrt(accel_x**2 + accel_z**2)) * 180 / math.pi
        accel_angle_y = math.atan2(-accel_x, math.sqrt(accel_y**2 + accel_z**2)) * 180 / math.pi
        
        # انتگرال‌گیری از ژایروسکوپ
        self.angle_x = self.alpha * (self.angle_x + gyro_x * self.dt) + (1 - self.alpha) * accel_angle_x
        self.angle_y = self.alpha * (self.angle_y + gyro_y * self.dt) + (1 - self.alpha) * accel_angle_y
        self.angle_z += gyro_z * self.dt  # زاویه Z فقط از ژایرو
        
        return self.angle_x, self.angle_y, self.angle_z
    
    def reset(self):
        """بازنشانی فیلتر"""
        self.angle_x = 0.0
        self.angle_y = 0.0
        self.angle_z = 0.0

class SensorDataFilter:
    """کلاس جامع فیلتر داده‌های سنسور"""
    
    def __init__(self, config_loader):
        """
        مقداردهی اولیه فیلتر داده‌های سنسور
        
        Args:
            config_loader: شیء بارگذار تنظیمات
        """
        self.config = config_loader
        self.enabled = self.config.get("sensor", "enable_filtering", True)
        
        if not self.enabled:
            return
        
        # تنظیمات فیلتر
        filter_type = self.config.get("sensor", "filter_type", "low_pass")
        alpha = self.config.get("sensor", "filter_alpha", 0.8)
        threshold = self.config.get("sensor", "noise_threshold", 0.01)
        
        # ایجاد فیلترها برای هر محور
        if filter_type == "low_pass":
            self.accel_filters = {
                'x': LowPassFilter(alpha),
                'y': LowPassFilter(alpha),
                'z': LowPassFilter(alpha)
            }
            self.gyro_filters = {
                'x': LowPassFilter(alpha),
                'y': LowPassFilter(alpha),
                'z': LowPassFilter(alpha)
            }
        elif filter_type == "moving_average":
            window_size = self.config.get("sensor", "window_size", 5)
            self.accel_filters = {
                'x': MovingAverageFilter(window_size),
                'y': MovingAverageFilter(window_size),
                'z': MovingAverageFilter(window_size)
            }
            self.gyro_filters = {
                'x': MovingAverageFilter(window_size),
                'y': MovingAverageFilter(window_size),
                'z': MovingAverageFilter(window_size)
            }
        
        # فیلتر آستانه نویز
        self.noise_filter = NoiseThresholdFilter(threshold)
        
        # فیلتر مکمل برای زوایا
        self.complementary_filter = ComplementaryFilter()
    
    def filter_data(self, sensor_data):
        """
        اعمال فیلتر به داده‌های سنسور
        
        Args:
            sensor_data (dict): داده‌های خام سنسور
            
        Returns:
            dict: داده‌های فیلتر شده
        """
        if not self.enabled:
            return sensor_data
        
        filtered_data = sensor_data.copy()
        
        try:
            # فیلتر داده‌های شتاب
            filtered_data['accel_x'] = self.accel_filters['x'].filter(sensor_data['accel_x'])
            filtered_data['accel_y'] = self.accel_filters['y'].filter(sensor_data['accel_y'])
            filtered_data['accel_z'] = self.accel_filters['z'].filter(sensor_data['accel_z'])
            
            # فیلتر داده‌های ژایرو
            filtered_data['gyro_x'] = self.gyro_filters['x'].filter(sensor_data['gyro_x'])
            filtered_data['gyro_y'] = self.gyro_filters['y'].filter(sensor_data['gyro_y'])
            filtered_data['gyro_z'] = self.gyro_filters['z'].filter(sensor_data['gyro_z'])
            
            # اعمال فیلتر آستانه نویز
            filtered_data['accel_x'] = self.noise_filter.filter(filtered_data['accel_x'])
            filtered_data['accel_y'] = self.noise_filter.filter(filtered_data['accel_y'])
            filtered_data['accel_z'] = self.noise_filter.filter(filtered_data['accel_z'])
            
            filtered_data['gyro_x'] = self.noise_filter.filter(filtered_data['gyro_x'])
            filtered_data['gyro_y'] = self.noise_filter.filter(filtered_data['gyro_y'])
            filtered_data['gyro_z'] = self.noise_filter.filter(filtered_data['gyro_z'])
            
            # محاسبه زوایای فیلتر شده
            angle_x, angle_y, angle_z = self.complementary_filter.update(
                filtered_data['accel_x'], filtered_data['accel_y'], filtered_data['accel_z'],
                filtered_data['gyro_x'], filtered_data['gyro_y'], filtered_data['gyro_z']
            )
            
            # اضافه کردن زوایای محاسبه شده
            filtered_data['angle_x'] = round(angle_x, 4)
            filtered_data['angle_y'] = round(angle_y, 4)
            filtered_data['angle_z'] = round(angle_z, 4)
            
            # گرد کردن مقادیر
            decimal_places = self.config.get("display", "decimal_places", 4)
            for key in ['accel_x', 'accel_y', 'accel_z', 'gyro_x', 'gyro_y', 'gyro_z']:
                filtered_data[key] = round(filtered_data[key], decimal_places)
            
        except Exception as e:
            print(f"⚠️  خطا در فیلتر داده‌ها: {e}")
            return sensor_data
        
        return filtered_data
    
    def reset_filters(self):
        """بازنشانی تمام فیلترها"""
        if not self.enabled:
            return
        
        for filter_obj in self.accel_filters.values():
            filter_obj.reset()
        
        for filter_obj in self.gyro_filters.values():
            filter_obj.reset()
        
        self.complementary_filter.reset()
        
        print("🔄 فیلترها بازنشانی شدند")
