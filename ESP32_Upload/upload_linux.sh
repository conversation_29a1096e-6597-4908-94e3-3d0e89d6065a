#!/bin/bash

echo "🚀 اسکریپت آپلود ESP32 برای Linux/Mac"
echo "======================================="

# بررسی وجود Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 نصب نیست"
    echo "💡 ابتدا Python3 را نصب کنید:"
    echo "   Ubuntu/Debian: sudo apt install python3 python3-pip"
    echo "   CentOS/RHEL: sudo yum install python3 python3-pip"
    echo "   macOS: brew install python3"
    exit 1
fi

# بررسی وجود pip
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 نصب نیست"
    echo "💡 pip3 را نصب کنید"
    exit 1
fi

# بررسی وجود ampy
if ! python3 -m pip show adafruit-ampy &> /dev/null; then
    echo "📦 در حال نصب ampy..."
    python3 -m pip install adafruit-ampy
    if [ $? -ne 0 ]; then
        echo "❌ خطا در نصب ampy"
        echo "💡 ممکن است نیاز به sudo داشته باشید:"
        echo "   sudo python3 -m pip install adafruit-ampy"
        exit 1
    fi
    echo "✅ ampy نصب شد"
fi

# یافتن پورت‌های موجود
echo ""
echo "🔍 پورت‌های موجود:"
if [ "$(uname)" == "Darwin" ]; then
    # macOS
    ls /dev/cu.* 2>/dev/null | head -5
else
    # Linux
    ls /dev/ttyUSB* /dev/ttyACM* 2>/dev/null | head -5
fi

# درخواست پورت از کاربر
echo ""
read -p "🔌 پورت ESP32 را وارد کنید (مثل /dev/ttyUSB0): " PORT

if [ -z "$PORT" ]; then
    echo "❌ پورت وارد نشده"
    exit 1
fi

# بررسی وجود پورت
if [ ! -e "$PORT" ]; then
    echo "❌ پورت $PORT وجود ندارد"
    exit 1
fi

# بررسی دسترسی به پورت
if [ ! -r "$PORT" ] || [ ! -w "$PORT" ]; then
    echo "❌ دسترسی به پورت $PORT وجود ندارد"
    echo "🔧 راه‌حل:"
    echo "   sudo usermod -a -G dialout \$USER"
    echo "   سپس logout/login کنید"
    echo ""
    echo "یا با sudo اجرا کنید:"
    echo "   sudo $0"
    exit 1
fi

echo ""
echo "📤 شروع آپلود فایل‌ها روی $PORT..."
echo ""

# تابع آپلود
upload_file() {
    local file=$1
    local num=$2
    local total=$3
    
    echo "[$num/$total] 📤 آپلود $file..."
    
    if ampy --port "$PORT" put "$file"; then
        echo "✅ $file آپلود شد"
        sleep 0.5  # کمی تاخیر
        return 0
    else
        echo "❌ خطا در آپلود $file"
        return 1
    fi
}

# فایل‌های مورد نیاز
files=(
    "config.json"
    "config_loader.py"
    "sensor_filters.py"
    "mpu6050_enhanced.py"
    "mpu6050_serial.py"
    "dataset_creator.py"
    "main.py"
)

# بررسی وجود فایل‌ها
missing_files=()
for file in "${files[@]}"; do
    if [ ! -f "$file" ]; then
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -ne 0 ]; then
    echo "❌ فایل‌های گمشده: ${missing_files[*]}"
    exit 1
fi

echo "✅ همه ${#files[@]} فایل موجود است"

# محاسبه حجم کل
total_size=0
for file in "${files[@]}"; do
    size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null)
    total_size=$((total_size + size))
done

echo "💾 حجم کل: $total_size bytes ($((total_size / 1024)) KB)"

# تایید آپلود
echo ""
echo "📤 آماده آپلود ${#files[@]} فایل:"
for i in "${!files[@]}"; do
    file="${files[$i]}"
    size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null)
    echo "   $((i+1)). $file ($size bytes)"
done

echo ""
read -p "❓ آیا می‌خواهید آپلود را شروع کنید؟ (y/n): " confirm

if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
    echo "⏹️  آپلود لغو شد"
    exit 0
fi

# شروع آپلود
echo ""
echo "🚀 شروع آپلود..."
start_time=$(date +%s)

success_count=0
failed_files=()

for i in "${!files[@]}"; do
    file="${files[$i]}"
    
    if upload_file "$file" $((i+1)) ${#files[@]}; then
        success_count=$((success_count + 1))
    else
        failed_files+=("$file")
    fi
done

# خلاصه نتایج
end_time=$(date +%s)
elapsed=$((end_time - start_time))

echo ""
echo "=================================================="
echo "📊 خلاصه آپلود:"
echo "   ⏱️  زمان: ${elapsed} ثانیه"
echo "   ✅ موفق: $success_count/${#files[@]}"
echo "   ❌ ناموفق: ${#failed_files[@]}"

if [ ${#failed_files[@]} -ne 0 ]; then
    echo "   🔴 فایل‌های ناموفق: ${failed_files[*]}"
    echo ""
    echo "🔧 برای حل مشکل:"
    echo "   - ESP32 را reset کنید"
    echo "   - دوباره تلاش کنید"
    exit 1
else
    echo ""
    echo "🎉 همه فایل‌ها با موفقیت آپلود شدند!"
    echo "🚀 ESP32 آماده استفاده است"
    echo ""
    echo "📡 برای تست:"
    echo "   python3 serial_reader.py --port $PORT --duration 30"
fi
