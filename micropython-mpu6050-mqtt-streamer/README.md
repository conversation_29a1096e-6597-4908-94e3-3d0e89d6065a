# Smart Helmet: Drowsiness Detection for Motorcyclists

This project aims to build a smart helmet that can detect the drowsiness of a motorcyclist and send alerts. It uses an MPU6050 accelerometer and gyroscope sensor to monitor the rider's head movements and an MQTT broker to stream the data for analysis.

## Project Structure

The project is divided into the following main components:

- **`sensor/`**: Contains the MicroPython code that runs on the ESP8266/ESP32 microcontroller. This code is responsible for reading sensor data and publishing it to an MQTT broker.
- **`sensorBrowser/`**: (Not used in this version)
- **`visulizer/`**: (Not used in this version)

## `sensor/main.py` - The Core Logic

The `sensor/main.py` file is the heart of the data collection system. It has been modified to collect data in 3-second windows, with a reading taken every 0.5 seconds.

### How It Works

1.  **Configuration**:
    *   `SAMPLE_INTERVAL_MS`: The time between each sensor reading, set to 500ms.
    *   `SAMPLE_WINDOW_S`: The total duration of each data sample, set to 3 seconds.
    *   `SAMPLES_PER_WINDOW`: The number of readings in each sample window, calculated to be 6.
    *   `MQTT_TOPIC`: The MQTT topic where the data is published (`micropython/smart_helmet/data`).

2.  **Main Loop**:
    *   The script enters an infinite loop to continuously collect data.
    *   In each iteration, it collects a 3-second sample window of data.

3.  **Data Collection**:
    *   A `for` loop runs `SAMPLES_PER_WINDOW` times to collect all the readings for a single window.
    *   In each iteration, it reads the accelerometer and gyroscope data from the MPU6050 sensor.
    *   The readings are stored in a list called `sample_window_data`.

4.  **Data Publishing**:
    *   After collecting all the readings for a window, the entire list is converted to a JSON string.
    *   This JSON string is then published to the specified MQTT topic.

### Classes and Methods

-   **`machine.I2C`**: Used to initialize the I2C communication with the MPU6050 sensor.
-   **`mpu6050.accel`**: A class from the `mpu6050.py` library to interact with the sensor.
    -   **`get_values()`**: Returns a dictionary containing the accelerometer and gyroscope data.
-   **`umqtt.simple.MQTTClient`**: Used to connect to the MQTT broker and publish data.
    -   **`connect()`**: Establishes a connection to the broker.
    -   **`publish()`**: Publishes a message to a specified topic.

## Installation and Usage

1.  **Hardware Setup**:
    *   Connect the MPU6050 sensor to your ESP8266/ESP32 microcontroller.
    *   Make sure you have the necessary libraries (`mpu6050.py`, `umqtt.simple.py`) on your device.

2.  **Software Setup**:
    *   Flash the MicroPython firmware onto your microcontroller.
    *   Upload the files from the `sensor/` directory to your device.

3.  **Running the Code**:
    *   The `boot.py` file will automatically run on startup, and then `main.py` will be executed.
    *   The device will start collecting data and publishing it to the MQTT broker.

4.  **Building the Dataset**:
    *   You can subscribe to the `micropython/smart_helmet/data` topic on the MQTT broker to receive the data.
    *   Each message will contain a JSON array of 6 readings, representing a 3-second sample.
    *   You can then save this data to a file to build your dataset for training a machine learning model.
