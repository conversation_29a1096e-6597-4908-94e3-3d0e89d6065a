"""
کد بهینه‌شده برای خواندن داده‌های سنسور MPU6050 و ذخیره در فایل CSV
ESP32 + MicroPython
نویسنده: توسعه‌دهنده پارسی

ویژگی‌ها:
- خو<PERSON>دن داده‌ها هر نیم ثانیه
- ذخیره در فایل CSV با بافرینگ
- بهینه‌سازی مصرف حافظه
- مدیریت خطا
"""

from machine import I2C, Pin
import time
import gc
import os

# وارد کردن تنظیمات
try:
    import config
except ImportError:
    print("فایل config.py یافت نشد. از تنظیمات پیش‌فرض استفاده می‌شود.")
    class config:
        I2C_SCL_PIN = 22
        I2C_SDA_PIN = 21
        I2C_FREQUENCY = 400000
        MPU6050_ADDRESS = 0x68
        CSV_FILENAME = "mpu6050_data.csv"
        BUFFER_SIZE = 5
        READ_INTERVAL_MS = 500
        SHOW_CONSOLE_OUTPUT = True
        DECIMAL_PLACES = 4
        ENABLE_GARBAGE_COLLECTION = True
        GC_THRESHOLD = 10

class MPU6050:
    """کلاس بهینه‌شده برای کار با سنسور MPU6050"""
    
    def __init__(self, i2c, addr=None):
        """
        مقداردهی اولیه سنسور
        Args:
            i2c: آبجکت I2C
            addr: آدرس سنسور (پیش‌فرض از config)
        """
        self.i2c = i2c
        self.addr = addr if addr is not None else config.MPU6050_ADDRESS
        self.sample_count = 0

        # بیدار کردن سنسور از حالت خواب
        self.i2c.writeto(self.addr, bytearray([0x6B, 0]))
        time.sleep_ms(100)  # انتظار برای آماده شدن سنسور

        # تنظیم محدوده شتاب‌سنج (±2g)
        self.i2c.writeto(self.addr, bytearray([0x1C, 0]))
        # تنظیم محدوده ژایروسکوپ (±250°/s)
        self.i2c.writeto(self.addr, bytearray([0x1B, 0]))

        print(f"سنسور MPU6050 در آدرس {hex(self.addr)} مقداردهی شد")
        
    def _bytes_to_int(self, high_byte, low_byte):
        """تبدیل دو بایت به عدد صحیح با علامت"""
        value = (high_byte << 8) | low_byte
        if value >= 32768:
            value -= 65536
        return value
    
    def read_raw_data(self):
        """خواندن داده‌های خام از سنسور"""
        try:
            # خواندن 14 بایت از رجیستر 0x3B
            raw_data = self.i2c.readfrom_mem(self.addr, 0x3B, 14)
            return raw_data
        except OSError as e:
            print(f"خطا در خواندن داده: {e}")
            return None
    
    def get_sensor_data(self):
        """
        دریافت داده‌های پردازش‌شده سنسور
        Returns:
            dict: شامل داده‌های شتاب و زاویه
        """
        raw_data = self.read_raw_data()
        if raw_data is None:
            return None
            
        # تبدیل داده‌های خام به مقادیر قابل استفاده
        accel_x = self._bytes_to_int(raw_data[0], raw_data[1]) / 16384.0  # g
        accel_y = self._bytes_to_int(raw_data[2], raw_data[3]) / 16384.0  # g
        accel_z = self._bytes_to_int(raw_data[4], raw_data[5]) / 16384.0  # g

        # دما (اختیاری)
        temp = self._bytes_to_int(raw_data[6], raw_data[7]) / 340.0 + 36.53  # °C

        gyro_x = self._bytes_to_int(raw_data[8], raw_data[9]) / 131.0   # °/s
        gyro_y = self._bytes_to_int(raw_data[10], raw_data[11]) / 131.0 # °/s
        gyro_z = self._bytes_to_int(raw_data[12], raw_data[13]) / 131.0 # °/s

        self.sample_count += 1

        return {
            'sample_id': self.sample_count,
            'timestamp': time.ticks_ms(),
            'accel_x': round(accel_x, config.DECIMAL_PLACES),
            'accel_y': round(accel_y, config.DECIMAL_PLACES),
            'accel_z': round(accel_z, config.DECIMAL_PLACES),
            'gyro_x': round(gyro_x, config.DECIMAL_PLACES),
            'gyro_y': round(gyro_y, config.DECIMAL_PLACES),
            'gyro_z': round(gyro_z, config.DECIMAL_PLACES),
            'temperature': round(temp, 2)
        }

class CSVLogger:
    """کلاس بهینه‌شده برای ذخیره داده‌ها در فایل CSV"""
    
    def __init__(self, filename=None, buffer_size=None):
        """
        مقداردهی اولیه لاگر CSV
        Args:
            filename: نام فایل CSV (پیش‌فرض از config)
            buffer_size: تعداد رکوردهای بافر قبل از نوشتن (پیش‌فرض از config)
        """
        self.filename = filename if filename is not None else config.CSV_FILENAME
        self.buffer_size = buffer_size if buffer_size is not None else config.BUFFER_SIZE
        self.buffer = []
        self.header_written = False
        self.total_records = 0
        self._check_file_exists()
    
    def _check_file_exists(self):
        """بررسی وجود فایل و نوشتن هدر در صورت نیاز"""
        try:
            with open(self.filename, 'r') as f:
                # اگر فایل وجود دارد و خالی نیست
                if f.read(1):
                    self.header_written = True
        except OSError:
            # فایل وجود ندارد
            pass
    
    def _write_header(self):
        """نوشتن هدر فایل CSV"""
        header = "sample_id,timestamp,accel_x,accel_y,accel_z,gyro_x,gyro_y,gyro_z,temperature\n"
        try:
            with open(self.filename, 'w') as f:
                f.write(header)
            self.header_written = True
            print(f"هدر فایل {self.filename} نوشته شد")
        except OSError as e:
            print(f"خطا در نوشتن هدر: {e}")
    
    def add_data(self, data):
        """
        افزودن داده به بافر
        Args:
            data: دیکشنری داده‌های سنسور
        """
        if data is None:
            return
            
        self.buffer.append(data)
        
        # نوشتن بافر در صورت پر شدن
        if len(self.buffer) >= self.buffer_size:
            self.flush_buffer()
    
    def flush_buffer(self):
        """نوشتن بافر در فایل"""
        if not self.buffer:
            return
            
        # نوشتن هدر در صورت نیاز
        if not self.header_written:
            self._write_header()
        
        try:
            with open(self.filename, 'a') as f:
                for data in self.buffer:
                    line = f"{data['sample_id']},{data['timestamp']},{data['accel_x']},{data['accel_y']},{data['accel_z']},"
                    line += f"{data['gyro_x']},{data['gyro_y']},{data['gyro_z']},{data['temperature']}\n"
                    f.write(line)

            self.total_records += len(self.buffer)
            print(f"{len(self.buffer)} رکورد در فایل ذخیره شد (مجموع: {self.total_records})")
            self.buffer.clear()

            # آزادسازی حافظه در صورت فعال بودن
            if config.ENABLE_GARBAGE_COLLECTION and self.total_records % config.GC_THRESHOLD == 0:
                gc.collect()
                print("حافظه آزادسازی شد")

        except OSError as e:
            print(f"خطا در نوشتن فایل: {e}")

def main():
    """تابع اصلی برنامه"""
    print("=" * 50)
    print("سیستم لاگ داده‌های MPU6050")
    print("=" * 50)

    # نمایش تنظیمات
    print(f"فایل خروجی: {config.CSV_FILENAME}")
    print(f"فاصله زمانی خواندن: {config.READ_INTERVAL_MS} میلی‌ثانیه")
    print(f"اندازه بافر: {config.BUFFER_SIZE} رکورد")
    print("-" * 50)

    # مقداردهی اولیه I2C و سنسور
    try:
        i2c = I2C(scl=Pin(config.I2C_SCL_PIN), sda=Pin(config.I2C_SDA_PIN), freq=config.I2C_FREQUENCY)
        mpu = MPU6050(i2c)
        print("سنسور MPU6050 با موفقیت مقداردهی شد")
    except Exception as e:
        print(f"خطا در مقداردهی سنسور: {e}")
        return

    # مقداردهی اولیه لاگر CSV
    logger = CSVLogger()
    
    # متغیرهای کنترل زمان
    last_read_time = time.ticks_ms()

    print("شروع جمع‌آوری داده‌ها...")
    print("برای توقف Ctrl+C را فشار دهید")
    print("-" * 50)

    try:
        while True:
            current_time = time.ticks_ms()

            # بررسی زمان برای خواندن داده
            if time.ticks_diff(current_time, last_read_time) >= config.READ_INTERVAL_MS:
                # خواندن داده از سنسور
                sensor_data = mpu.get_sensor_data()

                if sensor_data:
                    # افزودن داده به لاگر
                    logger.add_data(sensor_data)

                    # نمایش داده‌ها در کنسول (در صورت فعال بودن)
                    if config.SHOW_CONSOLE_OUTPUT:
                        print(f"نمونه {sensor_data['sample_id']:04d} | "
                              f"شتاب: ({sensor_data['accel_x']:+.3f}, {sensor_data['accel_y']:+.3f}, {sensor_data['accel_z']:+.3f}) g | "
                              f"زاویه: ({sensor_data['gyro_x']:+.3f}, {sensor_data['gyro_y']:+.3f}, {sensor_data['gyro_z']:+.3f}) °/s")

                last_read_time = current_time

            # کمی تاخیر برای جلوگیری از مصرف بالای CPU
            time.sleep_ms(10)
            
    except KeyboardInterrupt:
        print("\nمتوقف شدن برنامه...")
        # ذخیره داده‌های باقی‌مانده در بافر
        logger.flush_buffer()
        print("تمام داده‌ها ذخیره شدند")
    
    except Exception as e:
        print(f"خطای غیرمنتظره: {e}")
        # ذخیره داده‌های باقی‌مانده در صورت خطا
        logger.flush_buffer()

if __name__ == "__main__":
    main()
