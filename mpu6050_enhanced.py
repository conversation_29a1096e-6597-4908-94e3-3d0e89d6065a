"""
کد پیشرفته MPU6050 با فیلترهای نویز
ESP32 + MicroPython
شامل فیلترهای پیشرفته برای کاهش نویز و بهبود کیفیت داده‌ها

ویژگی‌ها:
- خواندن داده‌های شتاب و ژایرو در 3 محور
- فیلترهای پیشرفته نویز (Low-pass, Moving Average, Threshold)
- محاسبه زوایای فیلتر شده
- بهینه‌سازی مصرف حافظه
- مدیریت خطای پیشرفته
"""

import time
import gc
import os

try:
    from machine import I2C, Pin
    from config_loader import config_loader
    from sensor_filters import SensorDataFilter
    MICROPYTHON_MODE = True
except ImportError:
    # حالت تست - شبیه‌سازی
    MICROPYTHON_MODE = False
    import random
    
    # شبیه‌سازی config_loader
    class MockConfigLoader:
        def get(self, section, key, default=None):
            configs = {
                ("hardware", "i2c_scl_pin"): 22,
                ("hardware", "i2c_sda_pin"): 21,
                ("hardware", "i2c_frequency"): 400000,
                ("hardware", "mpu6050_address"): 104,
                ("sensor", "accel_scale"): 16384.0,
                ("sensor", "gyro_scale"): 131.0,
                ("sensor", "enable_filtering"): True,
                ("sensor", "filter_alpha"): 0.8,
                ("sensor", "noise_threshold"): 0.01,
                ("display", "decimal_places"): 4,
                ("csv", "buffer_size"): 5,
                ("memory", "enable_garbage_collection"): True,
                ("memory", "gc_threshold"): 10
            }
            return configs.get((section, key), default)
    
    config_loader = MockConfigLoader()
    
    # شبیه‌سازی SensorDataFilter
    class MockSensorDataFilter:
        def __init__(self, config):
            pass
        def filter_data(self, data):
            # اضافه کردن زوایای شبیه‌سازی شده
            data['angle_x'] = round(random.uniform(-180, 180), 4)
            data['angle_y'] = round(random.uniform(-180, 180), 4)
            data['angle_z'] = round(random.uniform(-180, 180), 4)
            return data
        def reset_filters(self):
            pass
    
    SensorDataFilter = MockSensorDataFilter

class MPU6050Enhanced:
    """کلاس پیشرفته سنسور MPU6050 با فیلترهای نویز"""
    
    # آدرس‌های رجیستر MPU6050
    PWR_MGMT_1 = 0x6B
    ACCEL_XOUT_H = 0x3B
    GYRO_XOUT_H = 0x43
    TEMP_OUT_H = 0x41
    
    def __init__(self, i2c, address=None):
        """
        مقداردهی اولیه سنسور MPU6050 پیشرفته
        
        Args:
            i2c: شیء I2C
            address: آدرس سنسور (اختیاری)
        """
        self.i2c = i2c
        self.address = address or config_loader.get("hardware", "mpu6050_address", 0x68)
        self.sample_count = 0
        
        # دریافت تنظیمات کالیبراسیون
        self.accel_scale = config_loader.get("sensor", "accel_scale", 16384.0)
        self.gyro_scale = config_loader.get("sensor", "gyro_scale", 131.0)
        
        # ایجاد فیلتر داده‌ها
        self.data_filter = SensorDataFilter(config_loader)
        
        # مقداردهی اولیه سنسور
        self._initialize_sensor()
    
    def _initialize_sensor(self):
        """مقداردهی اولیه سنسور MPU6050"""
        try:
            # بیدار کردن سنسور
            self.i2c.writeto_mem(self.address, self.PWR_MGMT_1, b'\x00')
            time.sleep_ms(100)
            print("✅ سنسور MPU6050 پیشرفته مقداردهی شد")
        except Exception as e:
            print(f"❌ خطا در مقداردهی سنسور: {e}")
            raise
    
    def _bytes_to_int(self, high_byte, low_byte):
        """تبدیل دو بایت به عدد صحیح با علامت"""
        value = (high_byte << 8) | low_byte
        return value - 65536 if value > 32767 else value
    
    def read_raw_data(self):
        """
        خواندن داده‌های خام از سنسور
        
        Returns:
            bytes: 14 بایت داده خام یا None در صورت خطا
        """
        try:
            # خواندن 14 بایت از آدرس شروع شتاب‌سنج
            return self.i2c.readfrom_mem(self.address, self.ACCEL_XOUT_H, 14)
        except Exception as e:
            print(f"⚠️  خطا در خواندن داده‌های خام: {e}")
            return None
    
    def get_sensor_data(self):
        """
        خواندن و پردازش داده‌های سنسور
        
        Returns:
            dict: داده‌های پردازش شده سنسور یا None در صورت خطا
        """
        raw_data = self.read_raw_data()
        if raw_data is None:
            return None
        
        self.sample_count += 1
        
        try:
            # تبدیل داده‌های خام به مقادیر فیزیکی
            accel_x = self._bytes_to_int(raw_data[0], raw_data[1]) / self.accel_scale  # g
            accel_y = self._bytes_to_int(raw_data[2], raw_data[3]) / self.accel_scale  # g
            accel_z = self._bytes_to_int(raw_data[4], raw_data[5]) / self.accel_scale  # g
            
            gyro_x = self._bytes_to_int(raw_data[8], raw_data[9]) / self.gyro_scale   # °/s
            gyro_y = self._bytes_to_int(raw_data[10], raw_data[11]) / self.gyro_scale # °/s
            gyro_z = self._bytes_to_int(raw_data[12], raw_data[13]) / self.gyro_scale # °/s
            
            temp_raw = self._bytes_to_int(raw_data[6], raw_data[7])
            temperature = temp_raw / 340.0 + 36.53  # °C
            
            # ایجاد دیکشنری داده‌های خام
            sensor_data = {
                'sample_id': self.sample_count,
                'timestamp': time.ticks_ms() if MICROPYTHON_MODE else int(time.time() * 1000),
                'accel_x': accel_x,
                'accel_y': accel_y,
                'accel_z': accel_z,
                'gyro_x': gyro_x,
                'gyro_y': gyro_y,
                'gyro_z': gyro_z,
                'temperature': round(temperature, 2)
            }
            
            # اعمال فیلترهای نویز
            filtered_data = self.data_filter.filter_data(sensor_data)
            
            return filtered_data
            
        except Exception as e:
            print(f"⚠️  خطا در پردازش داده‌ها: {e}")
            return None
    
    def reset_filters(self):
        """بازنشانی فیلترهای سنسور"""
        self.data_filter.reset_filters()
        self.sample_count = 0
        print("🔄 فیلترهای سنسور بازنشانی شدند")

class EnhancedCSVLogger:
    """کلاس پیشرفته لاگ CSV با پشتیبانی از زوایا"""
    
    def __init__(self, filename=None, buffer_size=None):
        """
        مقداردهی اولیه لاگر CSV پیشرفته
        
        Args:
            filename (str): نام فایل CSV
            buffer_size (int): اندازه بافر
        """
        self.filename = filename or config_loader.get("csv", "filename", "mpu6050_enhanced_data.csv")
        self.buffer_size = buffer_size or config_loader.get("csv", "buffer_size", 5)
        self.buffer = []
        self.total_records = 0
        
        # ایجاد فایل و نوشتن header
        self._write_header()
    
    def _write_header(self):
        """نوشتن header فایل CSV"""
        try:
            with open(self.filename, 'w') as f:
                # Header شامل داده‌های شتاب، ژایرو، زوایا و دما
                header = "sample_id,timestamp,accel_x,accel_y,accel_z,gyro_x,gyro_y,gyro_z,angle_x,angle_y,angle_z,temperature\n"
                f.write(header)
            print(f"📁 فایل CSV ایجاد شد: {self.filename}")
        except Exception as e:
            print(f"❌ خطا در ایجاد فایل CSV: {e}")
            raise
    
    def add_data(self, sensor_data):
        """
        اضافه کردن داده به بافر
        
        Args:
            sensor_data (dict): داده‌های سنسور
        """
        if sensor_data is None:
            return
        
        self.buffer.append(sensor_data)
        
        # ذخیره خودکار در صورت پر شدن بافر
        if len(self.buffer) >= self.buffer_size:
            self.flush_buffer()
    
    def flush_buffer(self):
        """ذخیره بافر در فایل"""
        if not self.buffer:
            return
        
        try:
            with open(self.filename, 'a') as f:
                for data in self.buffer:
                    # نوشتن داده‌ها شامل زوایا
                    line = f"{data['sample_id']},{data['timestamp']},"
                    line += f"{data['accel_x']},{data['accel_y']},{data['accel_z']},"
                    line += f"{data['gyro_x']},{data['gyro_y']},{data['gyro_z']},"
                    line += f"{data.get('angle_x', 0)},{data.get('angle_y', 0)},{data.get('angle_z', 0)},"
                    line += f"{data['temperature']}\n"
                    f.write(line)
            
            self.total_records += len(self.buffer)
            print(f"💾 {len(self.buffer)} رکورد ذخیره شد (مجموع: {self.total_records})")
            self.buffer.clear()
            
            # مدیریت حافظه
            if config_loader.get("memory", "enable_garbage_collection", True):
                gc.collect()
                
        except Exception as e:
            print(f"❌ خطا در ذخیره داده‌ها: {e}")

def main_enhanced():
    """تابع اصلی پیشرفته"""
    print("=" * 60)
    print("🚀 سیستم پیشرفته لاگ داده‌های MPU6050")
    print("=" * 60)
    print("✨ ویژگی‌های جدید:")
    print("   🔧 فیلترهای پیشرفته نویز")
    print("   📐 محاسبه زوایای فیلتر شده")
    print("   🎯 بهینه‌سازی بهبود یافته")
    print("=" * 60)
    
    try:
        # مقداردهی I2C
        if MICROPYTHON_MODE:
            scl_pin = config_loader.get("hardware", "i2c_scl_pin", 22)
            sda_pin = config_loader.get("hardware", "i2c_sda_pin", 21)
            frequency = config_loader.get("hardware", "i2c_frequency", 400000)
            
            i2c = I2C(scl=Pin(scl_pin), sda=Pin(sda_pin), freq=frequency)
            mpu = MPU6050Enhanced(i2c)
        else:
            # حالت تست
            mpu = MPU6050Enhanced(None)
        
        # مقداردهی لاگر
        logger = EnhancedCSVLogger()
        
        print("🎯 شروع جمع‌آوری داده‌های فیلتر شده...")
        print("📊 فرمت داده‌ها: شتاب(3) + ژایرو(3) + زاویه(3) + دما")
        print("-" * 60)
        
        # حلقه اصلی
        while True:
            start_time = time.ticks_ms() if MICROPYTHON_MODE else int(time.time() * 1000)
            
            # خواندن داده‌های فیلتر شده
            sensor_data = mpu.get_sensor_data()
            
            if sensor_data:
                # ذخیره داده
                logger.add_data(sensor_data)
                
                # نمایش داده‌ها
                if config_loader.get("display", "show_console_output", True):
                    print(f"📈 نمونه {sensor_data['sample_id']:04d} | "
                          f"شتاب: ({sensor_data['accel_x']:+.3f}, {sensor_data['accel_y']:+.3f}, {sensor_data['accel_z']:+.3f}) | "
                          f"ژایرو: ({sensor_data['gyro_x']:+.1f}, {sensor_data['gyro_y']:+.1f}, {sensor_data['gyro_z']:+.1f}) | "
                          f"زاویه: ({sensor_data.get('angle_x', 0):+.1f}°, {sensor_data.get('angle_y', 0):+.1f}°, {sensor_data.get('angle_z', 0):+.1f}°)")
            
            # مدیریت زمان‌بندی
            elapsed = (time.ticks_ms() if MICROPYTHON_MODE else int(time.time() * 1000)) - start_time
            interval = config_loader.get("timing", "read_interval_ms", 500)
            
            if elapsed < interval:
                sleep_time = interval - elapsed
                if MICROPYTHON_MODE:
                    time.sleep_ms(sleep_time)
                else:
                    time.sleep(sleep_time / 1000)
            
            # مدیریت حافظه دوره‌ای
            if (config_loader.get("memory", "enable_garbage_collection", True) and 
                sensor_data and sensor_data['sample_id'] % config_loader.get("memory", "gc_threshold", 10) == 0):
                gc.collect()
    
    except KeyboardInterrupt:
        print("\n⚠️  سیستم توسط کاربر متوقف شد")
        logger.flush_buffer()
        print("💾 داده‌های باقی‌مانده ذخیره شدند")
    
    except Exception as e:
        print(f"\n❌ خطای غیرمنتظره: {e}")
        if 'logger' in locals():
            logger.flush_buffer()
    
    finally:
        print("👋 پایان برنامه")

if __name__ == "__main__":
    main_enhanced()
