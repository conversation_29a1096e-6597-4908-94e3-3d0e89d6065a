"""
تست اسکریپت ایجاد دیتاست
این فایل برای تست عملکرد dataset_creator.py استفاده می‌شود
"""

import os
import time

def test_dataset_creator():
    """تست ایجاد دیتاست"""
    print("🧪 شروع تست ایجاد دیتاست")
    print("=" * 50)
    
    try:
        # وارد کردن ماژول
        from dataset_creator import DatasetCreator
        
        # ایجاد دیتاست تست با 3 نمونه
        creator = DatasetCreator("test_dataset")
        
        print("📊 ایجاد دیتاست تست با 3 نمونه...")
        created_files = creator.create_dataset(num_samples=3, rest_duration=1)
        
        # بررسی فایل‌های ایجاد شده
        print("\n🔍 بررسی فایل‌های ایجاد شده:")
        
        for filename in created_files:
            if os.path.exists(filename):
                # خواندن محتوای فایل
                with open(filename, 'r') as f:
                    lines = f.readlines()
                    print(f"✅ {filename}: {len(lines)} خط")
                    
                    # نمایش چند خط اول
                    print(f"   📄 محتوای نمونه:")
                    for i, line in enumerate(lines[:4]):  # 4 خط اول
                        print(f"      {i+1}: {line.strip()}")
                    if len(lines) > 4:
                        print(f"      ... و {len(lines)-4} خط دیگر")
            else:
                print(f"❌ {filename}: فایل یافت نشد")
        
        print("\n✅ تست با موفقیت انجام شد!")
        return True
        
    except Exception as e:
        print(f"❌ خطا در تست: {e}")
        return False

def cleanup_test_files():
    """پاک کردن فایل‌های تست"""
    print("\n🧹 پاک کردن فایل‌های تست...")
    
    import glob
    test_files = glob.glob("test_dataset_*.csv")
    
    for filename in test_files:
        try:
            os.remove(filename)
            print(f"🗑️  {filename} پاک شد")
        except:
            print(f"⚠️  نتوانست {filename} را پاک کند")

if __name__ == "__main__":
    # اجرای تست
    success = test_dataset_creator()
    
    if success:
        print("\n🎉 تست موفقیت‌آمیز بود!")
        
        # پرسش برای پاک کردن فایل‌های تست
        try:
            response = input("\n❓ آیا فایل‌های تست پاک شوند؟ (y/n): ").lower()
            if response in ['y', 'yes', 'بله']:
                cleanup_test_files()
        except:
            pass
    else:
        print("\n❌ تست ناموفق!")
