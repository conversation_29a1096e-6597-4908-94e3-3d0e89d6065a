"""
فایل main.py برای ESP32
این فایل پس از boot.py اجرا می‌شود و سیستم لاگ را شروع می‌کند
"""

import time

def auto_start():
    """شروع خودکار سیستم لاگ"""
    print("شروع خودکار سیستم لاگ...")
    print("برای لغو، Ctrl+C را فشار دهید")
    
    # تاخیر 5 ثانیه‌ای برای امکان لغو
    for i in range(5, 0, -1):
        print(f"شروع در {i} ثانیه...")
        time.sleep_ms(1000)
    
    try:
        from mpu6050_csv_logger import main
        main()
    except KeyboardInterrupt:
        print("سیستم لاگ متوقف شد")
    except Exception as e:
        print(f"خطا در اجرای سیستم لاگ: {e}")

if __name__ == "__main__":
    auto_start()
