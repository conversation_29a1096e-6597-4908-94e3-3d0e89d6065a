"""
اسکریپت ایجاد دیتاست MPU6050
این اسکریپت برای جمع‌آوری داده‌های سنسور MPU6050 و ایجاد دیتاست استفاده می‌شود
هر نمونه شامل 3 ثانیه داده با فاصله نیم ثانیه است
"""

import time
import os
from datetime import datetime
import gc

# وارد کردن ماژول‌های سنسور
try:
    from machine import I2C, Pin
    from mpu6050_enhanced import MPU6050Enhanced, EnhancedCSVLogger
    from config_loader import config_loader
    MICROPYTHON_MODE = True
    print("حالت MicroPython فعال")
except ImportError:
    print("حالت تست - شبیه‌سازی سنسور")
    MICROPYTHON_MODE = False
    import random

    # شبیه‌سازی config_loader برای تست
    class MockConfigLoader:
        def get(self, section, key, default=None):
            configs = {
                ("hardware", "i2c_scl_pin"): 22,
                ("hardware", "i2c_sda_pin"): 21,
                ("hardware", "i2c_frequency"): 400000,
                ("dataset", "samples_per_collection"): 6,
                ("dataset", "default_samples"): 10,
                ("dataset", "default_rest_duration"): 2
            }
            return configs.get((section, key), default)

    config_loader = MockConfigLoader()

    # شبیه‌سازی EnhancedCSVLogger برای تست
    class EnhancedCSVLogger:
        def __init__(self, filename, buffer_size=1):
            self.filename = filename
            self.buffer = []
            self.buffer_size = buffer_size
            self._write_header()

        def _write_header(self):
            with open(self.filename, 'w') as f:
                # Header شامل زوایا
                f.write("sample_id,timestamp,accel_x,accel_y,accel_z,gyro_x,gyro_y,gyro_z,angle_x,angle_y,angle_z,temperature\n")

        def add_data(self, data):
            self.buffer.append(data)
            if len(self.buffer) >= self.buffer_size:
                self.flush_buffer()

        def flush_buffer(self):
            if not self.buffer:
                return
            try:
                with open(self.filename, 'a') as f:
                    for data in self.buffer:
                        line = f"{data['sample_id']},{data['timestamp']},{data['accel_x']},{data['accel_y']},{data['accel_z']},"
                        line += f"{data['gyro_x']},{data['gyro_y']},{data['gyro_z']},"
                        line += f"{data.get('angle_x', 0)},{data.get('angle_y', 0)},{data.get('angle_z', 0)},"
                        line += f"{data['temperature']}\n"
                        f.write(line)
                self.buffer.clear()
            except Exception as e:
                print(f"خطا در نوشتن فایل: {e}")

    # شبیه‌سازی MPU6050Enhanced
    class MPU6050Enhanced:
        def __init__(self, i2c):
            self.sample_count = 0

        def get_sensor_data(self):
            self.sample_count += 1
            return {
                'sample_id': self.sample_count,
                'timestamp': int(time.time() * 1000),
                'accel_x': round(random.uniform(-2, 2), 4),
                'accel_y': round(random.uniform(-2, 2), 4),
                'accel_z': round(random.uniform(-2, 2), 4),
                'gyro_x': round(random.uniform(-250, 250), 4),
                'gyro_y': round(random.uniform(-250, 250), 4),
                'gyro_z': round(random.uniform(-250, 250), 4),
                'angle_x': round(random.uniform(-180, 180), 4),
                'angle_y': round(random.uniform(-180, 180), 4),
                'angle_z': round(random.uniform(-180, 180), 4),
                'temperature': round(random.uniform(20, 30), 2)
            }

class DatasetCreator:
    """کلاس ایجاد دیتاست MPU6050"""
    
    def __init__(self, base_filename="dataset_sample"):
        """
        مقداردهی اولیه ایجادکننده دیتاست
        
        Args:
            base_filename (str): نام پایه فایل‌های CSV
        """
        self.base_filename = base_filename
        self.mpu = None
        self.sample_count = 0
        self.total_samples = 0
        
        # مقداردهی سنسور در صورت وجود
        if MICROPYTHON_MODE:
            self._init_sensor()
    
    def _init_sensor(self):
        """مقداردهی اولیه سنسور MPU6050"""
        try:
            scl_pin = config_loader.get("hardware", "i2c_scl_pin", 22)
            sda_pin = config_loader.get("hardware", "i2c_sda_pin", 21)
            frequency = config_loader.get("hardware", "i2c_frequency", 400000)

            i2c = I2C(scl=Pin(scl_pin), sda=Pin(sda_pin), freq=frequency)
            self.mpu = MPU6050Enhanced(i2c)
            print("✅ سنسور MPU6050 پیشرفته با موفقیت مقداردهی شد")
            return True
        except Exception as e:
            print(f"❌ خطا در مقداردهی سنسور: {e}")
            return False
    
    def _get_mock_data(self, sample_id, timestamp):
        """تولید داده شبیه‌سازی شده برای تست"""
        return {
            'sample_id': sample_id,
            'timestamp': timestamp,
            'accel_x': round(random.uniform(-2, 2), 4),
            'accel_y': round(random.uniform(-2, 2), 4),
            'accel_z': round(random.uniform(-2, 2), 4),
            'gyro_x': round(random.uniform(-250, 250), 4),
            'gyro_y': round(random.uniform(-250, 250), 4),
            'gyro_z': round(random.uniform(-250, 250), 4),
            'temperature': round(random.uniform(20, 30), 2)
        }
    
    def collect_single_sample(self, sample_number):
        """
        جمع‌آوری یک نمونه 3 ثانیه‌ای داده
        
        Args:
            sample_number (int): شماره نمونه
            
        Returns:
            str: نام فایل ایجاد شده
        """
        # ایجاد نام فایل با timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{self.base_filename}_{sample_number:03d}_{timestamp}.csv"
        
        print(f"\n📊 شروع جمع‌آوری نمونه {sample_number}")
        print(f"📁 فایل: {filename}")
        print(f"⏱️  مدت زمان: 3 ثانیه (هر 0.5 ثانیه یک داده)")
        print("-" * 50)
        
        # ایجاد لاگر CSV پیشرفته
        logger = EnhancedCSVLogger(filename, buffer_size=1)  # بافر کوچک برای ذخیره فوری
        
        # متغیرهای کنترل زمان
        start_time = time.ticks_ms() if MICROPYTHON_MODE else int(time.time() * 1000)
        duration_ms = 3000  # 3 ثانیه
        interval_ms = 500   # نیم ثانیه
        expected_samples = config_loader.get("dataset", "samples_per_collection", 6)  # تعداد نمونه‌های مورد انتظار

        data_count = 0
        last_read_time = start_time - interval_ms  # شروع از قبل تا اولین داده فوری خوانده شود

        try:
            while data_count < expected_samples:
                current_time = time.ticks_ms() if MICROPYTHON_MODE else int(time.time() * 1000)

                # بررسی پایان مدت زمان (با کمی تساهل)
                elapsed = (time.ticks_diff(current_time, start_time) if MICROPYTHON_MODE
                          else current_time - start_time)

                if elapsed > duration_ms + 100:  # 100ms تساهل اضافی
                    print(f"⚠️  زمان تمام شد - {data_count} داده جمع‌آوری شد")
                    break

                # بررسی زمان خواندن داده
                time_diff = (time.ticks_diff(current_time, last_read_time) if MICROPYTHON_MODE
                           else current_time - last_read_time)

                if time_diff >= interval_ms:
                    data_count += 1
                    
                    # خواندن داده از سنسور یا شبیه‌سازی
                    if MICROPYTHON_MODE and self.mpu:
                        sensor_data = self.mpu.get_sensor_data()
                        if sensor_data:
                            sensor_data['sample_id'] = data_count
                    else:
                        sensor_data = self._get_mock_data(data_count, current_time)
                    
                    if sensor_data:
                        # ذخیره داده
                        logger.add_data(sensor_data)
                        
                        # نمایش پیشرفت
                        remaining_time = max(0, (duration_ms - elapsed) / 1000)
                        print(f"📈 داده {data_count:02d}/{expected_samples:02d} | "
                              f"شتاب: ({sensor_data['accel_x']:+.3f}, {sensor_data['accel_y']:+.3f}, {sensor_data['accel_z']:+.3f}) | "
                              f"باقی‌مانده: {remaining_time:.1f}s")
                    
                    last_read_time = current_time
                
                # تاخیر کوتاه برای جلوگیری از مصرف بالای CPU
                if MICROPYTHON_MODE:
                    time.sleep_ms(10)
                else:
                    time.sleep(0.01)
            
            # ذخیره نهایی داده‌ها
            logger.flush_buffer()
            
            print(f"✅ نمونه {sample_number} کامل شد - {data_count} داده ذخیره شد")
            
            # آزادسازی حافظه
            if MICROPYTHON_MODE:
                gc.collect()
            
            return filename
            
        except KeyboardInterrupt:
            print(f"\n⚠️  نمونه {sample_number} توسط کاربر متوقف شد")
            logger.flush_buffer()
            return filename
        except Exception as e:
            print(f"❌ خطا در جمع‌آوری نمونه {sample_number}: {e}")
            return None
    
    def create_dataset(self, num_samples, rest_duration=2):
        """
        ایجاد دیتاست کامل
        
        Args:
            num_samples (int): تعداد نمونه‌های مورد نیاز
            rest_duration (int): مدت زمان استراحت بین نمونه‌ها (ثانیه)
        """
        print("=" * 60)
        print("🎯 ایجاد دیتاست MPU6050")
        print("=" * 60)
        print(f"📊 تعداد نمونه‌ها: {num_samples}")
        print(f"⏱️  مدت هر نمونه: 3 ثانیه")
        print(f"🔄 فاصله بین نمونه‌ها: {rest_duration} ثانیه")
        print(f"📁 نام پایه فایل‌ها: {self.base_filename}")
        print("=" * 60)
        
        created_files = []
        successful_samples = 0
        
        try:
            for i in range(1, num_samples + 1):
                print(f"\n🔄 آماده‌سازی نمونه {i}/{num_samples}")
                
                # جمع‌آوری نمونه
                filename = self.collect_single_sample(i)
                
                if filename:
                    created_files.append(filename)
                    successful_samples += 1
                    print(f"✅ نمونه {i} با موفقیت ذخیره شد: {filename}")
                else:
                    print(f"❌ خطا در ایجاد نمونه {i}")
                
                # استراحت بین نمونه‌ها (به جز آخرین نمونه)
                if i < num_samples:
                    print(f"\n😴 استراحت {rest_duration} ثانیه...")
                    for j in range(rest_duration, 0, -1):
                        print(f"⏳ {j} ثانیه باقی‌مانده...", end="\r")
                        if MICROPYTHON_MODE:
                            time.sleep_ms(1000)
                        else:
                            time.sleep(1)
                    print(" " * 20, end="\r")  # پاک کردن خط
        
        except KeyboardInterrupt:
            print(f"\n⚠️  ایجاد دیتاست توسط کاربر متوقف شد")
        
        # خلاصه نهایی
        print("\n" + "=" * 60)
        print("📋 خلاصه ایجاد دیتاست")
        print("=" * 60)
        print(f"✅ نمونه‌های موفق: {successful_samples}/{num_samples}")
        print(f"📁 فایل‌های ایجاد شده:")
        
        for i, filename in enumerate(created_files, 1):
            file_size = self._get_file_size(filename)
            print(f"   {i:2d}. {filename} ({file_size})")
        
        if successful_samples == num_samples:
            print("\n🎉 دیتاست با موفقیت کامل شد!")
        else:
            print(f"\n⚠️  {num_samples - successful_samples} نمونه ناموفق")
        
        print("=" * 60)
        return created_files
    
    def _get_file_size(self, filename):
        """دریافت اندازه فایل"""
        try:
            if MICROPYTHON_MODE:
                import os
                size = os.stat(filename)[6]
            else:
                size = os.path.getsize(filename)
            
            if size < 1024:
                return f"{size} bytes"
            elif size < 1024 * 1024:
                return f"{size/1024:.1f} KB"
            else:
                return f"{size/(1024*1024):.1f} MB"
        except:
            return "نامشخص"

def main():
    """تابع اصلی برنامه"""
    print("🚀 خوش آمدید به ایجادکننده دیتاست MPU6050!")
    
    try:
        # دریافت تنظیمات از کاربر
        print("\n📝 تنظیمات دیتاست:")
        
        if MICROPYTHON_MODE:
            # در MicroPython از مقادیر config استفاده می‌کنیم
            num_samples = config_loader.get("dataset", "default_samples", 10)
            rest_duration = config_loader.get("dataset", "default_rest_duration", 2)
            base_filename = "dataset_sample"
        else:
            # در Python استاندارد از کاربر می‌پرسیم
            default_samples = config_loader.get("dataset", "default_samples", 10)
            default_rest = config_loader.get("dataset", "default_rest_duration", 2)

            num_samples = int(input(f"🔢 تعداد نمونه‌های مورد نیاز (مثال: {default_samples}): ") or str(default_samples))
            rest_duration = int(input(f"⏱️  فاصله بین نمونه‌ها (ثانیه، مثال: {default_rest}): ") or str(default_rest))
            base_filename = input("📁 نام پایه فایل‌ها (مثال: dataset_sample): ") or "dataset_sample"
        
        print(f"\n✅ تنظیمات تایید شد:")
        print(f"   📊 تعداد نمونه‌ها: {num_samples}")
        print(f"   ⏱️  فاصله بین نمونه‌ها: {rest_duration} ثانیه")
        print(f"   📁 نام پایه فایل‌ها: {base_filename}")
        
        # ایجاد دیتاست
        creator = DatasetCreator(base_filename)
        created_files = creator.create_dataset(num_samples, rest_duration)
        
        return created_files
        
    except KeyboardInterrupt:
        print("\n👋 خروج از برنامه")
    except Exception as e:
        print(f"\n❌ خطای غیرمنتظره: {e}")

if __name__ == "__main__":
    main()
